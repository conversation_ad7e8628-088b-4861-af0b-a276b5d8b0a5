#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
MarketDataManager测试模块

该模块用于测试市场数据管理器的功能，特别是股票条件检查功能。
测试内容包括：
1. 股票条件检查功能
2. 各种条件组合的测试
3. 边界条件测试
"""

import sys
import os
import unittest
from unittest.mock import MagicMock, patch
import logging

# 添加src目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../src')))

# 模拟外部依赖
sys.modules['mysql.connector'] = MagicMock()
sys.modules['mysql.connector.pooling'] = MagicMock()
sys.modules['mysql.connector.errors'] = MagicMock()

# 模拟 api.get_stock_price_ibapi 模块
mock_manager_res_dict = {}
mock_number_of_transactions_dict = {}
sys.modules['api.get_stock_price_ibapi'] = MagicMock()
sys.modules['api.get_stock_price_ibapi'].manager_res_dict_test = mock_manager_res_dict
sys.modules['api.get_stock_price_ibapi'].number_of_transactions_dict = mock_number_of_transactions_dict

# 配置日志
logging.basicConfig(level=logging.INFO)

# 导入常量和配置
from utils.constants import (
    NOW_PRICE, 
    VOLUME, 
    VOLUME_AVG, 
    NEWS, 
    STOCK_SHARESOUTSTANDING,
    SET_FLG,
    CHECK_VOLUME,
    BUY_FLG
)
from utils.config import STOCK_THRESHOLDS

# 创建模拟的key_generator
class MockKeyGenerator:
    @staticmethod
    def get_keys_for_stock(stock_code):
        return {
            NOW_PRICE: f"{stock_code}_now_price",
            VOLUME: f"{stock_code}_volume",
            VOLUME_AVG: f"{stock_code}_volume_avg",
            NEWS: f"{stock_code}_news",
            SET_FLG: f"{stock_code}_set_flg",
            STOCK_SHARESOUTSTANDING: f"{stock_code}_shares",
            CHECK_VOLUME: f"{stock_code}_check_volume",
            BUY_FLG: f"{stock_code}_buy_flg"
        }

# 替换实际的key_generator
with patch('utils.key_generator.key_generator', new=MockKeyGenerator()) as mock_key_generator:
    from utils.key_generator import key_generator
    # 导入真实的MarketDataManager
    from core.market_data_manager import MarketDataManager


class TestMarketDataManager(unittest.TestCase):
    """测试MarketDataManager类"""
    
    def setUp(self):
        """测试前的准备工作"""
        # 创建真实的数据管理器
        self.data_manager = MarketDataManager()
        
        # 测试股票代码
        self.stock_code = "TEST"
        
        # 获取股票键值
        self.keys = key_generator.get_keys_for_stock(self.stock_code)
        
        # 重置条件字典
        self.data_manager.all_conditions = {}
        self.data_manager.high_all_conditions = {}
        
    def test_data_reference(self):
        """测试_data是否正确引用"""
        # 设置模拟字典中的值
        test_key = "test_key"
        test_value = "test_value"
        self.data_manager.update_data(test_key, test_value)
        
        # 验证通过data_manager可以获取到相同的值
        self.assertEqual(self.data_manager.get_data(test_key), test_value)
    
    def test_check_stock_conditions_high_volume_avg(self):
        """测试平均成交量超过阈值的情况"""
        # 准备测试数据
        market_data = {
            VOLUME: 1000000,
            VOLUME_AVG: STOCK_THRESHOLDS['MAX_VOLUME_AVG'] + 1,  # 超过阈值
            NEWS: ["测试新闻"],
            STOCK_SHARESOUTSTANDING: 5000000
        }
        
        # 执行测试
        self.data_manager.check_stock_conditions(self.stock_code, market_data)
        
        # 验证结果 - 检查SET_FLG是否被正确设置
        self.assertTrue(self.keys[SET_FLG] in self.data_manager._data)
        self.assertEqual(self.data_manager._data[self.keys[SET_FLG]], True)
    
    def test_check_stock_conditions_no_news(self):
        """测试无新闻的情况"""
        # 准备测试数据
        market_data = {
            VOLUME: 1000000,
            VOLUME_AVG: 200000,  # 低于阈值
            NEWS: [],  # 无新闻
            STOCK_SHARESOUTSTANDING: 5000000
        }
        
        # 执行测试
        self.data_manager.check_stock_conditions(self.stock_code, market_data)
        
        # 验证结果 - 检查SET_FLG是否被正确设置
        self.assertTrue(self.keys[SET_FLG] in self.data_manager._data)
        self.assertEqual(self.data_manager._data[self.keys[SET_FLG]], True)
    
    def test_check_stock_conditions_high_shares(self):
        """测试流通股本超过阈值的情况"""
        # 准备测试数据
        market_data = {
            VOLUME: 1000000,
            VOLUME_AVG: 200000,
            NEWS: ["测试新闻"]
        }
        
        # 设置模拟字典中的流通股本和新闻
        self.data_manager.update_data(self.keys[STOCK_SHARESOUTSTANDING], STOCK_THRESHOLDS['MAX_SHARES_OUTSTANDING'] + 1)
        self.data_manager.update_data(self.keys[NEWS], ["测试新闻"])
        
        # 执行测试
        self.data_manager.check_stock_conditions(self.stock_code, market_data)
        
        # 验证结果 - 检查SET_FLG是否被正确设置
        self.assertTrue(self.keys[SET_FLG] in self.data_manager._data)
        self.assertEqual(self.data_manager._data[self.keys[SET_FLG]], True)
    
    def test_check_stock_conditions_check_volume(self):
        """测试需要检查成交量的情况"""
        # 准备测试数据
        market_data = {
            VOLUME: 300000,  # 低于平均成交量的5倍
            VOLUME_AVG: 200000
        }
        
        # 设置模拟字典中的流通股本和新闻
        self.data_manager.update_data(self.keys[STOCK_SHARESOUTSTANDING], 5000000)
        self.data_manager.update_data(self.keys[NEWS], ["测试新闻"])
        
        # 执行测试
        self.data_manager.check_stock_conditions(self.stock_code, market_data)
        
        # 验证结果 - 检查SET_FLG是否被正确设置
        self.assertTrue(self.keys[SET_FLG] in self.data_manager._data)
        self.assertEqual(self.data_manager._data[self.keys[SET_FLG]], True)
        # CHECK_VOLUME 应该被设置为 True
        self.assertTrue(market_data.get(CHECK_VOLUME, False))
    
    def test_check_stock_conditions_all_conditions_met(self):
        """测试所有条件都满足的情况"""
        # 准备测试数据
        market_data = {
            VOLUME: 1200000,  # 是平均成交量的6倍，满足条件
            VOLUME_AVG: 200000
        }
        
        # 设置模拟字典中的流通股本和新闻
        self.data_manager.update_data(self.keys[STOCK_SHARESOUTSTANDING], 5000000)
        self.data_manager.update_data(self.keys[NEWS], ["测试新闻"])
        
        # 执行测试
        self.data_manager.check_stock_conditions(self.stock_code, market_data)
        
        # 验证结果 - 应该在 all_conditions 中且值为 True
        self.assertTrue(self.data_manager.all_conditions.get(self.stock_code, False))
    
    def test_check_stock_conditions_exception_handling(self):
        """测试异常处理"""
        # 准备会引发异常的测试数据
        market_data = {
            VOLUME: "非数字",  # 会导致异常
            VOLUME_AVG: 200000,
            NEWS: ["测试新闻"],
            STOCK_SHARESOUTSTANDING: 5000000
        }
        
        # 执行测试
        try:
            self.data_manager.check_stock_conditions(self.stock_code, market_data)
            # 如果没有抛出异常，测试通过
            self.assertNotIn(self.stock_code, self.data_manager.all_conditions)
        except Exception as e:
            # 如果抛出异常，测试失败
            self.fail(f"check_stock_conditions抛出了异常: {e}")
    
    def test_check_stock_conditions_invalid_shares(self):
        """测试无效流通股本的情况"""
        # 准备测试数据
        market_data = {
            VOLUME: 1000000,
            VOLUME_AVG: 200000
        }
        
        # 设置模拟字典中的流通股本为无效值和新闻
        self.data_manager.update_data(self.keys[STOCK_SHARESOUTSTANDING], "非数字")
        self.data_manager.update_data(self.keys[NEWS], ["测试新闻"])
        
        # 执行测试
        self.data_manager.check_stock_conditions(self.stock_code, market_data)
        
        # 验证结果 - SET_FLG 应该被设置为 True，因为流通股本无效被视为无穷大
        self.assertTrue(self.keys[SET_FLG] in self.data_manager._data)
        self.assertEqual(self.data_manager._data[self.keys[SET_FLG]], True)
        
    def test_check_stock_conditions_changing_conditions(self):
        """测试条件变化的情况"""
        # 1. 首先测试 cond_shares and cond_news and not cond_volume_avg 为 true 的情况
        # 准备测试数据 - 成交量不高，有新闻，流通股本小
        market_data = {
            VOLUME: 300000,  # 低于平均成交量的5倍
            VOLUME_AVG: 200000
        }
        
        # 设置模拟字典中的流通股本和新闻
        self.data_manager.update_data(self.keys[STOCK_SHARESOUTSTANDING], 5000000)
        self.data_manager.update_data(self.keys[NEWS], ["测试新闻"])
        
        # 执行测试
        self.data_manager.check_stock_conditions(self.stock_code, market_data)
        
        # 验证结果 - 应该设置SET_FLG和CHECK_VOLUME
        self.assertTrue(self.keys[SET_FLG] in self.data_manager._data)
        self.assertEqual(self.data_manager._data[self.keys[SET_FLG]], True)
        self.assertTrue(market_data.get(CHECK_VOLUME, False))
        
        # 重置数据
        self.data_manager._data.clear()
        self.data_manager.all_conditions.clear()
        market_data.clear()
        
        # 2. 测试 cond_volume_avg 为 true 的情况
        # 准备测试数据 - 成交量高，有新闻，流通股本小
        market_data = {
            VOLUME: 1100000,  # 是平均成交量的5.5倍，满足条件
            VOLUME_AVG: 200000
        }
        
        # 设置模拟字典中的流通股本和新闻
        self.data_manager.update_data(self.keys[STOCK_SHARESOUTSTANDING], 5000000)
        self.data_manager.update_data(self.keys[NEWS], ["测试新闻"])
        
        # 执行测试
        self.data_manager.check_stock_conditions(self.stock_code, market_data)
        
        # 验证结果 - 应该在 all_conditions 中且值为 True，但不设置SET_FLG
        self.assertTrue(self.data_manager.all_conditions.get(self.stock_code, False))
        self.assertFalse(self.keys[SET_FLG] in self.data_manager._data)
        self.assertFalse(market_data.get(CHECK_VOLUME, False))
        
        # 重置数据
        self.data_manager._data.clear()
        self.data_manager.all_conditions.clear()
        market_data.clear()
        
        # 3. 测试 cond_news 为 false 的情况
        # 准备测试数据 - 成交量高，无新闻，流通股本小
        market_data = {
            VOLUME: 1100000,
            VOLUME_AVG: 200000
        }
        
        # 设置模拟字典中的流通股本，但不设置新闻
        self.data_manager.update_data(self.keys[STOCK_SHARESOUTSTANDING], 5000000)
        # 不设置新闻，或设置为空列表
        self.data_manager.update_data(self.keys[NEWS], [])
        
        # 执行测试
        self.data_manager.check_stock_conditions(self.stock_code, market_data)
        
        # 验证结果 - 应该设置SET_FLG，但不在 all_conditions 中
        self.assertTrue(self.keys[SET_FLG] in self.data_manager._data)
        self.assertEqual(self.data_manager._data[self.keys[SET_FLG]], True)
        self.assertFalse(self.stock_code in self.data_manager.all_conditions)
        
        # 重置数据
        self.data_manager._data.clear()
        self.data_manager.all_conditions.clear()
        market_data.clear()
        
        # 4. 测试 cond_shares 为 false 的情况
        # 准备测试数据 - 成交量高，有新闻，流通股本大
        market_data = {
            VOLUME: 1100000,
            VOLUME_AVG: 200000
        }
        
        # 设置模拟字典中的流通股本为超过阈值的值，设置新闻
        self.data_manager.update_data(self.keys[STOCK_SHARESOUTSTANDING], STOCK_THRESHOLDS['MAX_SHARES_OUTSTANDING'] + 1)
        self.data_manager.update_data(self.keys[NEWS], ["测试新闻"])
        
        # 执行测试
        self.data_manager.check_stock_conditions(self.stock_code, market_data)
        
        # 验证结果 - 应该设置SET_FLG，但不在 all_conditions 中
        self.assertTrue(self.keys[SET_FLG] in self.data_manager._data)
        self.assertEqual(self.data_manager._data[self.keys[SET_FLG]], True)
        self.assertFalse(self.stock_code in self.data_manager.all_conditions)
    
    def test_buy_flag_operations(self):
        """测试买入标志操作"""
        # 测试设置买入标志
        self.data_manager.set_buy_flag(self.stock_code, True)
        self.assertTrue(self.data_manager.is_buy_flag_set(self.stock_code))
        
        # 测试取消买入标志
        self.data_manager.set_buy_flag(self.stock_code, False)
        self.assertFalse(self.data_manager.is_buy_flag_set(self.stock_code))
    
    def test_update_buy_price(self):
        """测试更新买入价格"""
        test_price = 123.45
        self.data_manager.update_buy_price(self.stock_code, test_price)
        self.assertEqual(self.data_manager.get_data(self.stock_code), test_price)


if __name__ == "__main__":
    unittest.main() 