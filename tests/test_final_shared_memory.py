#!/usr/bin/env python3
"""
最终的共享内存测试
使用简单但有效的方案验证进程间数据共享
"""

import sys
import os
import time
import multiprocessing
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def create_simple_shared_data():
    """创建简单的共享数据结构"""
    manager = multiprocessing.Manager()
    return manager.dict({
        'stock_list': manager.list(),
        'realtime_bars': manager.dict(),
        'status': manager.dict({'connected': False, 'count': 0})
    })


def producer_process(shared_data):
    """生产者进程"""
    print("🚀 生产者进程启动...")
    
    try:
        # 推送股票列表
        shared_data['stock_list'].extend(['AAPL', 'GOOGL', 'MSFT'])
        print(f"📋 推送股票列表: {list(shared_data['stock_list'])}")
        
        # 推送状态
        shared_data['status']['connected'] = True
        shared_data['status']['count'] = len(shared_data['stock_list'])
        print(f"🔗 更新状态: {dict(shared_data['status'])}")
        
        # 推送实时数据
        for i in range(5):
            if 'AAPL' not in shared_data['realtime_bars']:
                shared_data['realtime_bars']['AAPL'] = manager.list()
            
            bar_data = {
                'time': f'20250622 09:3{i}:00',
                'price': 150.0 + i,
                'volume': 1000000 + i * 100000
            }
            shared_data['realtime_bars']['AAPL'].append(bar_data)
            print(f"📊 推送AAPL数据: {bar_data}")
            time.sleep(0.2)
        
        print("✅ 生产者进程完成")
        
    except Exception as e:
        print(f"❌ 生产者进程出错: {e}")
        import traceback
        traceback.print_exc()


def consumer_process(shared_data):
    """消费者进程"""
    print("🌐 消费者进程启动...")
    
    try:
        # 等待生产者启动
        time.sleep(1)
        
        print("👂 开始读取共享数据...")
        
        for i in range(10):
            # 读取股票列表
            stock_list = list(shared_data['stock_list'])
            print(f"📋 股票列表: {stock_list}")
            
            # 读取状态
            status = dict(shared_data['status'])
            print(f"🔗 状态: {status}")
            
            # 读取实时数据
            if 'AAPL' in shared_data['realtime_bars']:
                aapl_data = list(shared_data['realtime_bars']['AAPL'])
                print(f"📊 AAPL数据条数: {len(aapl_data)}")
                if aapl_data:
                    print(f"    最新数据: {aapl_data[-1]}")
            
            time.sleep(0.5)
        
        print("✅ 消费者进程完成")
        
    except Exception as e:
        print(f"❌ 消费者进程出错: {e}")
        import traceback
        traceback.print_exc()


def test_with_our_api(shared_data):
    """使用我们的API测试"""
    print("🧪 使用我们的API测试...")
    
    try:
        from src.api.shared_memory import (
            push_stock_list_to_frontend,
            push_realtime_bar_to_frontend
        )
        from ibapi.common import BarData
        
        # 创建测试数据
        def create_test_bar_data(stock_code: str, time_str: str, price: float) -> BarData:
            bar = BarData()
            bar.date = time_str
            bar.open = price
            bar.high = price + 1.0
            bar.low = price - 1.0
            bar.close = price + 0.5
            bar.volume = 1000000
            return bar
        
        # 推送数据
        push_stock_list_to_frontend(['AAPL', 'GOOGL'])
        
        bar_data = create_test_bar_data('AAPL', '20250622 09:30:00', 150.0)
        push_realtime_bar_to_frontend('AAPL', bar_data)
        
        print("✅ API测试完成")
        
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("🧪 开始最终共享内存测试")
    print("=" * 60)
    
    # 先测试我们的API
    test_with_our_api(None)
    
    print("\n" + "=" * 60)
    
    # 创建共享数据
    print("📦 创建共享数据结构...")
    shared_data = create_simple_shared_data()
    
    # 创建进程
    producer = multiprocessing.Process(
        target=producer_process, 
        args=(shared_data,), 
        name="Producer"
    )
    consumer = multiprocessing.Process(
        target=consumer_process, 
        args=(shared_data,), 
        name="Consumer"
    )
    
    try:
        # 启动进程
        print("🚀 启动进程...")
        producer.start()
        consumer.start()
        
        # 等待完成
        producer.join(timeout=10)
        consumer.join(timeout=10)
        
        # 清理
        if producer.is_alive():
            print("⚠️  生产者进程超时，强制终止")
            producer.terminate()
        if consumer.is_alive():
            print("⚠️  消费者进程超时，强制终止")
            consumer.terminate()
        
        # 最终数据检查
        print("\n📊 最终数据状态:")
        print(f"  股票列表: {list(shared_data['stock_list'])}")
        print(f"  状态: {dict(shared_data['status'])}")
        if 'AAPL' in shared_data['realtime_bars']:
            print(f"  AAPL数据条数: {len(shared_data['realtime_bars']['AAPL'])}")
        
        print("\n" + "=" * 60)
        print("🎉 最终共享内存测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 设置多进程启动方法
    multiprocessing.set_start_method('spawn', force=True)
    main()
