<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>5分钟K线开盘价修复测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .pass { color: green; }
        .fail { color: red; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>5分钟K线开盘价修复测试</h1>
    
    <div class="test-section">
        <h2>测试场景：模拟副系统回放模式下的开盘价更新问题</h2>
        <div id="test-results"></div>
    </div>

    <script type="module">
        // 模拟ChartManager类的关键方法
        class TestChartManager {
            constructor() {
                this.oneMinData = new Map();
                this.fiveMinData = new Map();
            }

            // 复制修复后的aggregateOnePeriodEnhanced方法
            aggregateOnePeriodEnhanced(oneMinArr, existingCandle = null) {
                if (!oneMinArr.length) return null;
                
                const validData = oneMinArr.filter(d => 
                    d && typeof d.time === 'number' && 
                    typeof d.open === 'number' && 
                    typeof d.high === 'number' && 
                    typeof d.low === 'number' && 
                    typeof d.close === 'number' &&
                    typeof d.volume === 'number' &&
                    isFinite(d.open) && isFinite(d.high) && 
                    isFinite(d.low) && isFinite(d.close) &&
                    d.volume > 0
                );
                
                if (!validData.length) return null;
                
                const sorted = validData.sort((a, b) => a.time - b.time);
                const periodStart = Math.floor(sorted[0].time / 300) * 300;
                
                return {
                    time: periodStart,
                    open: existingCandle ? existingCandle.open : sorted[0].open, // 修复：保持原开盘价
                    high: Math.max(...sorted.map(d => d.high)),
                    low: Math.min(...sorted.map(d => d.low)),
                    close: sorted[sorted.length - 1].close,
                    volume: sorted.reduce((sum, d) => sum + d.volume, 0)
                };
            }

            // 复制修复后的batchUpdate5MinCandles方法
            batchUpdate5MinCandles(fiveMinArr, newCandles) {
                if (!newCandles.length) return fiveMinArr;
                
                const sortedNew = [...newCandles].sort((a, b) => a.time - b.time);
                let mergedArr = [];
                let oldIndex = 0, newIndex = 0;
                
                while (oldIndex < fiveMinArr.length && newIndex < sortedNew.length) {
                    const oldCandle = fiveMinArr[oldIndex];
                    const newCandle = sortedNew[newIndex];
                    
                    if (oldCandle.time < newCandle.time) {
                        mergedArr.push(oldCandle);
                        oldIndex++;
                    } else if (oldCandle.time > newCandle.time) {
                        mergedArr.push(newCandle);
                        newIndex++;
                    } else {
                        // 修复：合并数据但保持原有开盘价
                        const mergedCandle = {
                            ...newCandle,
                            open: oldCandle.open
                        };
                        mergedArr.push(mergedCandle);
                        oldIndex++;
                        newIndex++;
                    }
                }
                
                while (oldIndex < fiveMinArr.length) {
                    mergedArr.push(fiveMinArr[oldIndex++]);
                }
                while (newIndex < sortedNew.length) {
                    mergedArr.push(sortedNew[newIndex++]);
                }
                
                return mergedArr;
            }
        }

        // 测试函数
        function runTests() {
            const manager = new TestChartManager();
            const results = [];

            // 测试1：初始5分钟K线创建
            console.log('=== 测试1：初始5分钟K线创建 ===');
            const initialData = [
                { time: 1751463000, open: 3.48, high: 3.55, low: 3.31, close: 3.40, volume: 100000 }
            ];
            
            const firstCandle = manager.aggregateOnePeriodEnhanced(initialData);
            const test1Pass = firstCandle.open === 3.48;
            results.push({
                name: '测试1：初始K线开盘价',
                pass: test1Pass,
                expected: 3.48,
                actual: firstCandle.open,
                description: '首次创建5分钟K线时，开盘价应该是第一个1分钟数据的开盘价'
            });

            // 测试2：更新已存在的5分钟K线（关键测试）
            console.log('=== 测试2：更新已存在的5分钟K线 ===');
            const existingCandle = { time: 1751463000, open: 3.48, high: 3.55, low: 3.31, close: 3.40, volume: 100000 };
            const updateData = [
                { time: 1751463060, open: 3.405, high: 3.50, low: 3.265, close: 3.415, volume: 50000 }
            ];
            
            const updatedCandle = manager.aggregateOnePeriodEnhanced(updateData, existingCandle);
            const test2Pass = updatedCandle.open === 3.48; // 应该保持原开盘价
            results.push({
                name: '测试2：更新时保持开盘价',
                pass: test2Pass,
                expected: 3.48,
                actual: updatedCandle.open,
                description: '更新已存在的5分钟K线时，开盘价应该保持不变'
            });

            // 测试3：批量合并测试
            console.log('=== 测试3：批量合并测试 ===');
            const oldFiveMin = [
                { time: 1751463000, open: 3.48, high: 3.55, low: 3.31, close: 3.40, volume: 100000 }
            ];
            const newCandles = [
                { time: 1751463000, open: 3.405, high: 3.50, low: 3.265, close: 3.415, volume: 150000 }
            ];
            
            const merged = manager.batchUpdate5MinCandles(oldFiveMin, newCandles);
            const test3Pass = merged[0].open === 3.48; // 应该保持原开盘价
            results.push({
                name: '测试3：批量合并保持开盘价',
                pass: test3Pass,
                expected: 3.48,
                actual: merged[0].open,
                description: '批量合并时，相同时间的K线应该保持原开盘价'
            });

            // 显示测试结果
            displayResults(results);
        }

        function displayResults(results) {
            const container = document.getElementById('test-results');
            let html = '<h3>测试结果</h3>';
            
            results.forEach(result => {
                const status = result.pass ? 'pass' : 'fail';
                const statusText = result.pass ? '✅ 通过' : '❌ 失败';
                
                html += `
                    <div class="test-case">
                        <h4 class="${status}">${result.name} - ${statusText}</h4>
                        <p><strong>描述：</strong>${result.description}</p>
                        <p><strong>期望值：</strong>${result.expected}</p>
                        <p><strong>实际值：</strong>${result.actual}</p>
                    </div>
                `;
            });

            const passCount = results.filter(r => r.pass).length;
            const totalCount = results.length;
            
            html += `
                <div class="summary">
                    <h3>测试总结</h3>
                    <p class="${passCount === totalCount ? 'pass' : 'fail'}">
                        通过：${passCount}/${totalCount} 
                        ${passCount === totalCount ? '🎉 所有测试通过！' : '⚠️ 存在失败的测试'}
                    </p>
                </div>
            `;
            
            container.innerHTML = html;
        }

        // 页面加载后运行测试
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
