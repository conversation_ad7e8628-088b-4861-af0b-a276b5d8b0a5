import pandas as pd
import numpy as np
import logging
from datetime import datetime
import pytz
from src.utils.constants import (
    BREAKOUT_FLG,
    BREAKOUT_RED_FLG,
    NY_TZ
)
from src.utils.key_generator import key_generator

# 获取当前时间
start_date = datetime.now(NY_TZ).strftime('%Y-%m-%d')

# 将时间边界计算移出循环
start_check = pd.Timestamp(f'{start_date} 09:38:00')
end_check = pd.Timestamp(f'{start_date} 11:50:00')
ref_start = pd.Timestamp(f'{start_date} 09:30:00')
ref_end = pd.Timestamp(f'{start_date} 09:35:00')

start_check_red = pd.Timestamp(f'{start_date} 09:32:00')
end_check_red = pd.Timestamp(f'{start_date} 10:00:00')

def check_breakout(stock_code: str, df: pd.DataFrame, manager_res_dict_test: dict) -> None:
    """开盘后第一根5分钟突破"""
    
    # 获取股票的所有键值
    keys = key_generator.get_keys_for_stock(stock_code)

    manager_res_dict_test[keys[BREAKOUT_FLG]] = False
    # 获取最新数据点的时间
    latest_time = df.index[-1]  # 使用-1确保正确获取最后一个元素
    
    # 检查是否在有效时间窗口内
    if not (start_check <= latest_time <= end_check):
        return
    
    # 计算参考最高价 (09:31:00~09:35:00)
    df_ref = df.loc[ref_start:ref_end, 'High'] # 利用切片语法
    if df_ref.empty:
        logging.info(f'{stock_code} 参考区间无数据')
        return
    max_high = df_ref.max()

    if np.isnan(max_high) or max_high <= 0:
        logging.warning(f"{stock_code} 无效参考最高价: {max_high}")
        return

    # 获取最新数据点（合并操作提升性能）
    df_len = len(df)
    latest_data = {
        'high': df['High'].values[-1] if df_len >= 1 else np.nan,
        'open': df['Open'].values[-1] if df_len >= 1 else np.nan,
        'close': df['Close'].values[-1] if df_len >= 1 else np.nan,
        'volume': df['Volume'].values[-1] if df_len >= 1 else np.nan,
        'prev_close': df['Close'].values[-2] if df_len >= 2 else np.nan
    }
    # 数据有效性检查
    if any(np.isnan(val) for val in latest_data.values()):
        logging.error(f"{stock_code} 数据包含无效值")
        return

    # 条件判断
    condition_volume = latest_data['volume'] > 0  # 排除零成交量
    condition_positive_candle = latest_data['open'] < latest_data['close']  # 阳线
    condition_break_high = latest_data['high'] >= max_high  # 突破前高
    condition_prev_confined = latest_data['prev_close'] <= max_high  # 前收盘未突破

    if all([condition_volume, 
            condition_positive_candle,
            condition_break_high,
            condition_prev_confined]):
        logging.info(f'{stock_code} 突破前高 {max_high}, 当前最高 {latest_data["high"]}')
        manager_res_dict_test[keys[BREAKOUT_FLG]] = True

def check_breakout_red(stock_code: str, df: pd.DataFrame, manager_res_dict_test: dict) -> None:
    """检查股票是否符合突破前一根红K线条件
    
    策略逻辑：
    1. 时间窗口有效性检查（必须在指定时间范围内）
    2. 第三根K线（前绿K）必须为涨幅≥3%的阳线
    3. 第二根K线必须为阴线且收盘价高于第三根K线中点
    4. 最新K线必须为阳线且开盘价高于第三根K线中点
    
    Args:
        stock_code (str): 股票代码
        df (pd.DataFrame): 包含OHLC数据的DataFrame，按时间升序排列
        manager_res_dict_test (dict): 存储检查结果的字典
    """

    # 获取股票的所有键值
    keys = key_generator.get_keys_for_stock(stock_code)

    manager_res_dict_test[keys[BREAKOUT_RED_FLG]] = False
    
    # 数据有效性检查：至少需要3根K线才能进行分析
    if len(df) < 3:
        logging.debug(f"{stock_code} 数据不足，需要至少3根K线，当前只有{len(df)}根")
        return
    
    # 获取最新数据点时间并检查时间窗口
    latest_time = df.index[-1]
    if not (start_check_red <= latest_time <= end_check_red):
        return

    # 提前提取关键数据点（使用.iloc[-3/-2/-1]对应最新三根K线）
    # 第三根K线（前绿K候选）
    third_close = df['Close'].iloc[-3]
    third_open = df['Open'].iloc[-3]
    third_high = df['High'].iloc[-3]
    third_low = df['Low'].iloc[-3]
    
    # 第二根K线（中间调整K线）
    second_close = df['Close'].iloc[-2]
    second_open = df['Open'].iloc[-2]
    
    # 最新K线（当前突破候选）
    first_close = df['Close'].iloc[-1]
    first_open = df['Open'].iloc[-1]
    
    # 预计算前绿K中点和涨幅
    third_mid = (third_high + third_low) * 0.5  # 前绿K中点价格
    third_increase_pct = (third_close - third_open) / third_open * 100  # 前绿K涨幅百分比

    # 条件1: 前绿K必须为阳线（收盘>开盘）
    if third_close <= third_open:
        return
    
    # 条件2: 中间K线必须为阴线（收盘<=开盘）
    if second_close > second_open:
        return
    
    # 条件3: 当前K线必须为阳线（收盘>开盘）
    if first_close < first_open:
        return
    
    # 条件4: 前绿K涨幅需≥3%
    if third_increase_pct < 3:
        return
    
    # 条件5: 中间K线收盘需高于前绿K中点
    if second_close < third_mid:
        return
    
    # 条件6: 当前K线开盘需高于前绿K中点
    if first_open < third_mid:
        return

    # 所有条件满足，标记突破成立
    manager_res_dict_test[keys[BREAKOUT_RED_FLG]] = True
