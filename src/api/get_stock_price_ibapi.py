"""
Interactive Brokers API 集成模块

本模块提供了与Interactive Brokers (IB) API的集成功能，用于获取实时市场数据和执行交易操作。
主要功能包括：
1. 市场数据订阅和处理
2. 连接管理和自动重连
3. 性能监控和优化
4. 错误处理和恢复机制
5. 数据缓存和批处理

主要组件：
- MyWrapper: IB API回调处理类
- MyClient: IB API客户端类
- IBApp: 综合应用类，结合Wrapper和Client功能

技术细节：
- 使用数据类存储市场数据
- 实现线程池管理并发请求
- 使用锁机制保护共享资源
- 实现批处理优化性能
- 提供自动重连和错误恢复

依赖：
- ibapi: Interactive Brokers API
- pandas: 数据处理
- numpy: 数值计算
- schedule: 任务调度
- threading: 多线程支持
- logging: 日志记录
"""

import time
from datetime import datetime, timedelta
from typing import Any, Dict
from decimal import Decimal
import threading
import pandas as pd
import asyncio
from src.analysis.check_the_signal import main_
from src.analysis.pre_stock_signal import check_stock_volume
from src.utils.futu_utils import modify_the_watchlist
import logging
import logging.config
import schedule
import numpy as np
import subprocess
import collections
from concurrent.futures import ThreadPoolExecutor, as_completed
import os
import shutil
from collections import defaultdict
import uuid
from pathlib import Path
from src.config.config import LOG_CONFIG
import psutil  # 添加CPU监控
# from apscheduler.schedulers.blocking import BlockingScheduler

from ibapi.client import EClient
from ibapi import wrapper
from ibapi.scanner import ScannerSubscription
from ibapi.contract import Contract
from ibapi.utils import *
from ibapi.common import *
from ibapi.contract import ContractDetails
from ibapi.ticktype import TickType
from ibapi.common import TickAttrib, TickAttribLast, TickAttribBidAsk
from futu import *
from futu import OpenQuoteContext as FutuQuoteContext

from src.utils.constants import *
from src.config.config import STOCK_THRESHOLDS, WEB_CONFIG
from src.services.globenewswire_rss import get_globenewswire_news
from src.services.stocktitan_rss import get_stocktitan_news
from src.utils.stock_utils import *
from src.utils.yfinance_fun import get_stock_sharesOutstanding
from src.analysis.tradingview_screener_q import tradingview_screener_stock
from src.utils.db_utils import get_todays_news
from src.analysis.macd_check import macd_signal
from src.analysis.check_breakout_level import check_breakout, check_breakout_red
from src.utils.restart_IB_Gateway import restart_tws
from src.analysis.number_of_transactions import check_number_of_transactions
from src.config.config import IB_CONFIG
from src.utils.key_generator import key_generator
from src.analysis.stock_analyzer import analyze_stock_data as analyzer_analyze_stock_data
from src.utils.analysis_formatter import format_analysis_results, build_analysis_results, create_analysis_results_from_analyzer
from src.utils.result_update_utils import update_manager_res_dict_test, set_manager_res_dict_test_default
from src.utils.cache_utils import handle_price_change_and_cache, clean_expired_data
from src.utils.resource_manager import ResourceManager, ResourceStatus
from src.data.market_data_manager import MarketDataManager  # 添加这行导入

# 定时任务
# blocking_scheduler = BlockingScheduler()

# 全局变量 - 使用新的线程安全数据管理器适配器
from src.data.data_manager_adapter import (
    manager_res_dict_test_adapter as manager_res_dict_test,
    number_of_transactions_dict_adapter as number_of_transactions_dict,
    manager_res_dict_adapter as manager_res_dict
)

# 常量定义
MAX_RETRY_ATTEMPTS = 3
"""最大重试次数 3次"""
MAX_SYMBOLS = 45
"""最大订阅符号数量 45"""
RETRY_INTERVAL = 5
"""重试间隔 5秒"""
CALL_INTERVAL = 8
"""调用间隔 8秒"""
NEWS_EXPIRE_SECONDS = 59 * 5
"""新闻过期时间 59 * 5 秒"""
MIN_LOW_AMOUNT = 170000
"""最小交易金额 170000"""
MAX_WORKERS = 12
"""最大工作线程数 12"""

# IB Gateway配置
IB_HOST = "127.0.0.1"
IB_PORT = 7497
IB_CLIENT_ID = 1000

# 市场扫描仪配置
SCANNER_ROWS = 30
"""扫描仪行数 30行"""
SCANNER_MIN_PRICE = 0.13
"""扫描仪最小价格 0.13"""
SCANNER_MAX_PRICE = 30
"""扫描仪最大价格 30"""
SCANNER_MIN_VOLUME = 20000
"""扫描仪最小成交量 20000"""

# 时间配置
MARKET_OPEN_HOUR = 9
"""市场开盘时间 9点"""
MARKET_CLOSE_HOUR = 16
"""市场收盘时间 16点"""
PRE_MARKET_START_HOUR = 4
"""盘前市场开始时间 4点"""
POST_MARKET_END_HOUR = 20
"""盘后市场结束时间 20点"""

# 连接重试配置
MAX_CONNECT_ATTEMPTS = 3
"""最大连接重试次数 3次"""
CONNECT_RETRY_INTERVAL = 5
"""连接重试间隔 5秒"""

# 数据缓存字典，格式：{(stock_code, timestamp_str): {'timestamp': datetime, 'ticker_data': df, 'orderbook_data': df, 'base_price': float}}
data_cache = {}
"""数据缓存字典"""
data_cache_lock = threading.Lock()
"""数据缓存字典锁"""


class MyWrapper(wrapper.EWrapper):
    """IB API回调处理类
    
    这个类处理来自IB API的所有回调事件，包括：
    1. 市场数据更新
    2. 历史数据
    3. 合约详情
    4. 错误处理
    5. 连接状态
    
    属性:
        data (dict): 存储市场数据
        contractDetails_dict (dict): 存储合约详情
        executor (ThreadPoolExecutor): 线程池执行器
        
    注意:
        - 所有回调方法都是线程安全的
        - 使用锁机制保护共享资源
        - 实现了数据缓存和批处理
    """
    def __init__(self):
        """初始化Wrapper类"""
        wrapper.EWrapper.__init__(self)
        
        # 创建MarketDataManager实例
        self.market_data_manager = MarketDataManager()
        
        # 基础数据结构
        self.data = {}
        """数据字典"""
        self.contractDetails_dict = {}
        """合约详情字典"""
        self.new_contractDetails_dict = {}
        """新合约详情字典"""
        self.reqId_dict = {}
        """请求ID字典"""
        self.reqId_dict_day = {}
        """请求ID字典"""
        self.reqId_dict_wrapper = {}
        """请求ID字典"""
        
        # 逐笔数据缓冲区
        self.tick_all_last_buffer = defaultdict(list)  # 自动处理新键
        """逐笔成交数据缓冲区"""
        self.ticker_data = defaultdict(lambda: pd.DataFrame())
        """逐笔成交数据"""
        # 逐笔报价数据缓冲区
        self.tick_bid_ask_buffer = defaultdict(list)  # 自动处理新键
        """逐笔报价数据缓冲区"""
        self.orderbook_data = defaultdict(lambda: pd.DataFrame())
        """逐笔报价数据"""
        
        # 逐笔请求限制相关
        self.active_tick_requests = {}  # 记录活跃的逐笔成交请求
        """活跃的逐笔成交请求"""
        self.tick_reqId_stock_dict = {}  # 记录逐笔成交请求ID和股票代码
        """逐笔成交请求ID和股票代码"""
        self.active_BidAsk_requests = {}  # 记录活跃的逐笔报价请求
        """活跃的逐笔报价请求"""
        self.bid_ask_reqId_stock_dict = {}  # 记录逐笔报价请求ID和股票代码
        """逐笔报价请求ID和股票代码"""
        self.inactive_tick = set()  # 记录已取消的逐笔请求
        """已取消的逐笔请求"""
        self.TICK_REQUEST_INTERVAL = 17  # 请求间隔(秒)
        """逐笔请求间隔"""
        self.last_tick_request_time = {}  # 记录上次请求逐笔成交的时间
        """上次请求逐笔成交的时间"""
        
        # 监控列表
        self.pre_symbol_list_pre = [None] * 15
        """预前15个股票代码"""
        self.symbol_set = set()
        """股票代码集合"""
        self.watch_dict = {}
        """监控列表"""
        self.pre_symbol_list_backup = set()
        """预前15个股票代码备份"""
        self.pre_historicalData = {}
        """预前历史数据"""
        self.filtered_list_backup = []
        """过滤列表备份"""
        
        # 计数器和限制
        self.stock_count = 10000
        """股票数量"""
        self.stock_count_day = 1000000
        """股票 1天 reqid"""
        self.stock_count_tick = 2000000
        """股票数量"""
        self.stock_count_tick_bid_ask = 3000000
        """股票数量"""
        self.min_low_amount = 170000
        """最小交易金额"""
        
        # 状态标志
        self.start = False
        """启动标志"""
        self.play_flg_dict = {}
        """播放标志字典"""
        
        # 价格相关
        self.bid_price = None
        """买价"""
        self.ask_price = None
        """卖价"""
        
        # 新闻相关
        self.news_stock_dict = collections.defaultdict(int)
        """新闻股票字典"""
        self.EXPIRE_SECONDS = 59 * 5
        """过期时间"""
        
        # 锁和同步
        self.lock = threading.Lock()
        """锁"""
        self.news_stock_dict_lock = threading.Lock()
        """新闻股票字典锁"""
        self.sound_lock = threading.Lock()  # 添加声音锁
        """声音锁"""
        
        # 当前日期
        self.curr_date = datetime.now(tz=NY_TZ).strftime('%Y-%m-%d')
        """当前日期"""

        self.sound_warn = None
        """声音警告"""
        self.sound_process = None
        """声音处理"""

        self.web_stock_data = None
        """Web股票数据"""

    def nextValidId(self, orderId: int) -> None:
        """处理下一个有效订单ID的回调

        Args:
            orderId (int): IB提供的下一个有效订单ID

        注意:
            这个方法标志着与IB的连接已经建立
        """
        super().nextValidId(orderId)
        print("NextValidId:", orderId)
        self.start = True

        # 推送基本的IB API连接状态（详细信息将在连接完成后推送）
        # try:
        #     print("=== 开始推送IB API连接状态 ===", flush=True)
        #     sync_push_ib_api_status_to_frontend(
        #         is_connected=True,
        #         server_version="连接中...",
        #         connection_time="连接中...",
        #         active_stocks_count=get_active_stock_count()
        #     )
        #     print("=== IB API连接状态推送调用完成 ===", flush=True)
        # except Exception as e:
        #     print(f"=== 推送IB API连接状态失败: {e} ===", flush=True)
            # logging.error(f"推送IB API连接状态失败: {e}")

    def error(self, reqId: TickerId, errorCode: int, errorString: str, advancedOrderRejectJson: str = "") -> None:
        """处理错误回调
        
        Args:
            reqId (TickerId): 请求ID
            errorCode (int): 错误代码
            errorString (str): 错误描述
            advancedOrderRejectJson (str): 高级订单拒绝信息
        """
        code = reqId
        if reqId in self.reqId_dict_wrapper:
            code = self.reqId_dict_wrapper[reqId]
        if reqId in self.tick_reqId_stock_dict:
            code = self.tick_reqId_stock_dict[reqId]
        if reqId in self.bid_ask_reqId_stock_dict:
            code = self.bid_ask_reqId_stock_dict[reqId]
        logging.info(f'Error. ID: {code} Code: {errorCode} Msg: {errorString} AdvancedOrderRejectJson: {advancedOrderRejectJson}')
        
        if errorCode == 10190:  # 逐笔请求限制错误
            logging.warning(f"已达到逐笔请求的最大值，请求ID: {reqId}")
        elif errorCode == 1102 and '未连接' in errorString:
            self.start = False

    def connectionClosed(self) -> None:
        """TWS/IB Gateway 连接关闭时回调"""
        print("连接已关闭")
        self.start = False

        # 推送IB API断开连接状态
        # try:
        #     sync_push_ib_api_status_to_frontend(
        #         is_connected=False,
        #         server_version="",
        #         connection_time="",
        #         active_stocks_count=0
        #     )
        # except Exception as e:
        #     logging.error(f"推送IB API断开连接状态失败: {e}")

    def contractDetails(self, reqId: int, contractDetails: ContractDetails) -> None:
        # print('合同详情', reqId, contractDetails.contract.primaryExchange)
        # logging.info(f'合同详情 {reqId} {contractDetails.contract.primaryExchange}')
        if contractDetails.contract.primaryExchange in ['NASDAQ', 'AMEX', 'NYSE']:
            self.contractDetails_dict[reqId] = contractDetails.contract

    def tickPrice(self, reqId: TickerId, tickType: TickType, price: float, attrib: TickAttrib) -> None:
        """处理价格更新回调
        
        Args:
            reqId: 请求ID
            tickType: 价格类型（bid, ask等）
            price: 更新的价格
            attrib: 价格属性
            
        处理：
        1. 验证价格有效性
        2. 更新内部数据结构
        3. 触发必要的计算和更新
        4. 发送价格变化通知
        """
        stock_code = self.reqId_dict_wrapper[reqId]
        # 标记价格是否发生变化
        price_changed = False

        if tickType == 1:  # Bid Price
            if self.bid_price != price:  # 买价发生变化
                self.bid_price = price
                price_changed = True
        elif tickType == 2:  # Ask Price
            if self.ask_price != price:  # 卖价发生变化
                self.ask_price = price
                price_changed = True

        # 仅在价格发生变化且两者均已更新时调用判断函数
        if price_changed and self.bid_price is not None and self.ask_price is not None:
            self.market_data_manager.set(stock_code, IS_SPREAD_NORMAL, 
                is_spread_normal(price, self.bid_price, self.ask_price))

    def tickSize(self, reqId: TickerId, tickType: TickType, size: Decimal) -> None:
        """
        处理市场数据的 tickSize 回调函数。

        参数:
        - reqId: 请求 ID，用于标识当前请求。
        - tickType: 数据类型，8 表示成交量数据。
        - size: 数据值（对于成交量，是以"手"为单位的数量）。
        """
        if tickType == 8:  # Tick Type 8 表示成交量数据
            # 对于美国股票，返回值需乘以 100，将"手"转换为股数
            # print(f'{self.reqId_dict_wrapper[reqId]} tickType == 8 {format_volume(size * 100)}')

            volume = float(size * 100)
            self.market_data_manager.set(self.reqId_dict_wrapper[reqId], VOLUME, volume)

            if self.market_data_manager.get(self.reqId_dict_wrapper[reqId], CHECK_VOLUME, False) \
                and volume > self.market_data_manager.get(self.reqId_dict_wrapper[reqId], VOLUME_AVG, float('inf')) * STOCK_THRESHOLDS['VOLUME_AVG_MULTIPLIER']:
                self.market_data_manager.set_market_data(self.reqId_dict_wrapper[reqId], {
                    CHECK_VOLUME: False,
                    SET_FLG: False
                })
        # elif tickType == 21:  # Tick Type 21 表示 90 天的平均日成交量
        #     # 返回的值需要乘以 100，获取实际的平均日交易量
        #     # print(f'{self.reqId_dict_wrapper[reqId]} tickType == 21 pppppppppp')
        #     self.manager_res_dict[f'{self.reqId_dict_wrapper[reqId]}_{VOLUME_90_AVG}'] = format_volume(size * 100)

    def tickGeneric(self, reqId: TickerId, tickType: TickType, value: float) -> None:
        if tickType == 55:  # Tick Type 55 每分钟的交易次数
            # 使用键值生成器获取所有需要的键
            keys = key_generator.get_keys_for_stock(self.reqId_dict_wrapper[reqId])
            self.market_data_manager.set(self.reqId_dict_wrapper[reqId], NUMBER_FLG, value)
            # logging.info(f'{self.reqId_dict_wrapper[reqId]} 每分钟交易次数 {value}')
    # def tickString(self, reqId, tickType, value):
    #     if tickType == 77:  # RT Trade Volume
    #         # print(f'{self.reqId_dict_wrapper[reqId]} tickType == 77 {format_volume(value)}')
    #         # 使用分号分割字符串
    #         fields = value.split(';')

    #         # 提取第三个字段（总交易量），并转换为整数
    #         total_volume = int(fields[2])
    #         self.manager_res_dict[f'{self.reqId_dict_wrapper[reqId]}_{VOLUME}'] = format_volume(total_volume)
    # 接收历史数据结束
    def historicalDataEnd(self, reqId: int, start: str, end: str) -> None:

        stock_code = self.reqId_dict_wrapper.get(reqId)
        if not stock_code:
            return
        # 使用键值生成器获取所有需要的键
        keys = key_generator.get_keys_for_stock(stock_code)
        # 处理分钟K线数据请求 获取前一交易日盘中收盘价
        if reqId < 1000000:
            req_data = self.data.get(reqId, {})
            # 按交易日分组
            trading_days = {}
            
            # 获取当前日期
            current_date = datetime.now(tz=NY_TZ).strftime('%Y%m%d')
            
            for date_time in req_data.keys():
                # 解析时间戳
                # IB API格式："20250620 08:16:00 US/Eastern"
                date_part = date_time.split()[0]  # 获取日期部分 "20250620"
                time_part = date_time.split()[1]  # 获取时间部分 "08:16:00"
                
                # 时间部分格式为 HH:MM:SS
                hour = int(time_part.split(':')[0])
                minute = int(time_part.split(':')[1])
                
                # 只考虑常规交易时间内的K线 (9:30 - 15:59)
                is_regular_hours = (9 <= hour < 16) and not (hour == 9 and minute < 30)
                
                # 只处理当前日期之前的数据
                if date_part < current_date and is_regular_hours:
                    if date_part not in trading_days:
                        trading_days[date_part] = []
                    trading_days[date_part].append(date_time)
            
            # 按日期排序
            sorted_days = sorted(trading_days.keys())
            if sorted_days:  # 如果有交易日数据
                # 获取最近的一个交易日（当前日期之前的最后一个交易日）
                prev_trading_day = sorted_days[-1]
                
                # 获取该交易日盘中最后一根K线
                if trading_days[prev_trading_day]:
                    # 按时间排序
                    sorted_date_time = sorted(trading_days[prev_trading_day])
                    last_regular_kline = sorted_date_time[-1]
                    
                    # 获取收盘价
                    prev_day_close = req_data[last_regular_kline]["Close"]
                    self.market_data_manager.set(stock_code, PREV_DAY_CLOSE, prev_day_close)
                    logging.info(f'{stock_code} {last_regular_kline} {prev_day_close}')
        
        # 处理日线数据请求
        if reqId <= 1_0_0_0_0_0_0:
            return
        req_data = self.data.get(reqId, {})
        dates = sorted(req_data.keys())

        # 取前30天（排除最新一天）
        avg_volume_30d = np.mean([req_data[d]['Volume'] for d in dates[-31:-1] if d in req_data]) or 1
        
        self.market_data_manager.set_market_data(stock_code, {
            VOLUME_AVG: float(avg_volume_30d),
            SET_FLG: False
        })
        
        # # 打印最后两个交易日的close价格
        # if len(dates) >= 2:
        #     logging.info(f"{stock_code} 最后两个交易日close: {dates[-2]}={req_data[dates[-2]]['Close']}, {dates[-1]}={req_data[dates[-1]]['Close']}")
            
        self.market_data_manager.set(stock_code, HISTORICAL_DATA_END, 1)

        logging.info(f'historicalDataEnd {stock_code} {avg_volume_30d}')

    def historicalData(self, reqId: int, bar: BarData) -> None:
        # print("历史数据:", bar)
        if bar.volume < 0:
            return
        if reqId not in self.data:
            self.data[reqId] = {}
        self.data[reqId][bar.date] = {
            "date_time": bar.date,
            "Open": bar.open,
            "High": bar.high,
            "Low": bar.low,
            "Close": bar.close,
            "Volume": float(bar.volume)
        }

        if reqId > 100000:
            return
        # 获取并缓存股票代码
        stock_code = self.reqId_dict_wrapper.get(reqId)
        if not stock_code:
            return
        
        # 新增：Web股票实时数据字典
        self.web_stock_data[stock_code] = self.data[reqId]

    def historicalDataUpdate(self, reqId: int, bar: BarData) -> None:
        # 提前缓存高频访问的字典和键值
        data = self.data
        reqId_dict_wrapper = self.reqId_dict_wrapper
        new_contract_details = self.new_contractDetails_dict

        if bar.volume < 0:
            return
        # 主数据存储逻辑 - 修复：确保reqId对应的数据结构存在
        vol_float = float(bar.volume)
        if reqId not in data:
            data[reqId] = {}
        req_data = data[reqId]
        date_str = bar.date
        req_data[date_str] = {
            "date_time": date_str,
            "Open": bar.open,
            "High": bar.high,
            "Low": bar.low,
            "Close": bar.close,
            "Volume": vol_float
        }

        # 获取并缓存股票代码
        stock_code = reqId_dict_wrapper.get(reqId)
        if not stock_code:
            return

        # 新增：Web股票实时数据字典 - 现在req_data始终有效
        self.web_stock_data[stock_code] = req_data

        # 调试日志：记录关键的close价格更新（可在生产环境中注释掉）
        # if logging.getLogger().isEnabledFor(logging.DEBUG):
        #     logging.debug(f"更新Web数据 {stock_code}: close={bar.close}, time={date_str}")
        
        # 使用键值生成器获取所有需要的键
        keys = key_generator.get_keys_for_stock(stock_code)
        
        # 实时价格更新
        current_price = float(bar.close)
        self.market_data_manager.set(stock_code, NOW_PRICE, current_price)
        
        base_price = manager_res_dict.get(stock_code, 0)
        # 根据价格变化播放或停止声音
        if base_price != 0:  # 防止除以零错误
            price_change = (current_price - base_price) / base_price

            buy_flag = self.market_data_manager.get(stock_code, BUY_FLG, False)
            if self.play_flg_dict.get(stock_code, False):  # 当前正在播放声音

                    if price_change >= -0.042 or not buy_flag:  # 价格回升或卖出，停止播放
                        self.play_flg_dict[stock_code] = False
                        self._stop_sound(stock_code)
            else:  # 当前没有播放声音
                if price_change < -0.042 and buy_flag:  # 价格下跌超过4.2%且已买入
                    self.play_flg_dict[stock_code] = True  # 开始播放
                    self._play_sound()
        
        # 合约监控逻辑优化
        contract_detail = new_contract_details.get(reqId)
        if contract_detail is not None:
            number_flag = self.market_data_manager.get(stock_code, NUMBER_FLG, 0)
            if number_flag > 300:
                # 预计算关键数值
                avg_price = (bar.open + bar.high + bar.low + bar.close) * 0.25
                trade_value = avg_price * vol_float
                if trade_value > self.min_low_amount or self.market_data_manager.get(stock_code, NEWS, False):
                    # logging.info(f'激活合同监控 {stock_code}')
                    self.watch_dict[contract_detail] = stock_code
                    new_contract_details[reqId] = None

    def deduplicate_and_sort(self, watch_dict):
        """去重并排序字典"""
        seen_values = set()
        return {
            k: v for k, v in sorted(watch_dict.items()) if not (v in seen_values or seen_values.add(v))
        }
    
    # 获取逐笔数据
    def tickByTickAllLast(self, reqId: int, tickType: int, time: int, price: float, size: Decimal, tickAtrribLast: TickAttribLast, exchange: str, specialConditions: str) -> None:
        """处理逐笔成交数据

        Args:
            reqId: 请求ID
            tickType: 交易类型
            time: 时间戳
            price: 价格
            size: 数量
            tickAtrribLast: 交易属性
            exchange: 交易所
            specialConditions: 特殊条件
        """

        # 修复：添加锁保护，防止与analyze_realtime_data中的读取操作产生数据竞争
        with self.lock:
            # 保留原始时间戳，不再转换为字符串格式
            stock_code = self.tick_reqId_stock_dict[reqId]

            # 添加数据到缓冲区，包含UUID
            self.tick_all_last_buffer[stock_code].append({
                'uuid': str(uuid.uuid4()),  # 添加UUID
                'stock_code': stock_code,
                'date_time': time,  # 使用时间戳整数
                'price': float(price),
                'size': float(size),
                'exchange': exchange,
                'special_conditions': specialConditions
            })

    # 获取历史逐笔数据
    def historicalTicksLast(self, reqId: int, ticks: ListOfHistoricalTickLast, done: bool) -> None:
        print(f"{self.tick_reqId_stock_dict[reqId]} historicalTicksLast: {len(ticks)}")
        for tick in ticks:
            # 使用原始时间戳
            stock_code = self.tick_reqId_stock_dict[reqId]
            self.tick_all_last_buffer[stock_code].append({
                'uuid': str(uuid.uuid4()),  # 添加UUID
                'stock_code': stock_code,
                'date_time': tick.time,  # 使用时间戳整数
                'price': float(tick.price),
                'size': float(tick.size),
                'exchange': tick.exchange,
                'special_conditions': tick.specialConditions
            })
    
    # 获取深度摆盘数据
    def tickByTickBidAsk(self, reqId: int, time: int, bidPrice: float, askPrice: float, bidSize: Decimal, askSize: Decimal, tickAttribBidAsk: TickAttribBidAsk) -> None:
        """处理逐笔买卖盘数据

        Args:
            reqId: 请求ID
            time: 时间戳
            bidPrice: 买价
            askPrice: 卖价
            bidSize: 买量
            askSize: 卖量
            tickAttribBidAsk: 买卖盘属性
        """
        # 修复：添加锁保护，防止与analyze_realtime_data中的读取操作产生数据竞争
        with self.lock:
            # 使用原始时间戳
            stock_code = self.bid_ask_reqId_stock_dict[reqId]
            # 将数据暂存到缓冲区，包含UUID
            self.tick_bid_ask_buffer[stock_code].append({
                'uuid': str(uuid.uuid4()),  # 添加UUID
                'stock_code': stock_code,
                'date_time': time,  # 使用时间戳整数
                'bid_price': float(bidPrice),
                'ask_price': float(askPrice),
                'bid_size': float(bidSize),
                'ask_size': float(askSize),
                'bid_past_low': tickAttribBidAsk.bidPastLow,
                'ask_past_high': tickAttribBidAsk.askPastHigh
            })

    def historicalTicksBidAsk(self, reqId: int, ticks: ListOfHistoricalTickLast, done: bool) -> None:
        """处理历史买卖盘数据
        
        Args:
            reqId: 请求ID
            ticks: 历史tick数据列表
            done: 是否完成
        """
        print(f"{self.bid_ask_reqId_stock_dict[reqId]} historicalTicksBidAsk: {len(ticks)}")
        for tick in ticks:
            # 使用原始时间戳
            stock_code = self.bid_ask_reqId_stock_dict[reqId]
            self.tick_bid_ask_buffer[stock_code].append({
                'uuid': str(uuid.uuid4()),  # 添加UUID
                'stock_code': stock_code,
                'date_time': tick.time,  # 使用时间戳整数
                'bid_price': float(tick.priceBid),
                'ask_price': float(tick.priceAsk),
                'bid_size': float(tick.sizeBid),
                'ask_size': float(tick.sizeAsk),
                'bid_past_low': tick.tickAttribBidAsk.bidPastLow,
                'ask_past_high': tick.tickAttribBidAsk.askPastHigh
            })

    def _play_sound(self):
        """播放声音提醒
        
        该方法负责播放声音提醒，并更新声音进程资源。
        
        特性：
        - 线程安全（使用sound_lock）
        - 自动资源管理（通过resource_manager）
        - 错误处理和日志记录
        
        异常：
            Exception: 播放声音时发生的错误会被记录但不会抛出
        """
        try:
            with self.sound_lock:
                # 更新声音进程资源
                self.sound_process = subprocess.Popen(['afplay', self.sound_warn])
                self.resource_manager._resources['sound_process'] = self.sound_process
        except Exception as e:
            logging.error(f"播放声音时发生错误: {str(e)}", exc_info=True)

    def _stop_sound(self, stock_code):
        """停止声音的内部方法"""
        with self.sound_lock:
            self.play_flg_dict[stock_code] = False
            if self.sound_process and self.sound_process.poll() is None:
                try:
                    self.sound_process.terminate()
                    self.sound_process = None
                except Exception as e:
                    logging.error(f"停止声音时发生错误: {e}", exc_info=True)
            
    def get_resource_status(self) -> Dict[str, Any]:
        """获取所有资源的状态"""
        status = {}
        for resource_id in self.resource_manager._resources.keys():
            try:
                status[resource_id] = self.resource_manager.get_resource_info(resource_id)
            except Exception as e:
                logging.error(f"获取资源 {resource_id} 状态时发生错误: {str(e)}")
                status[resource_id] = {'status': 'ERROR', 'error': str(e)}
        return status

class MyClient(EClient):
    def __init__(self, wrapper: MyWrapper) -> None:
        EClient.__init__(self, wrapper)

class IBApp(MyWrapper, MyClient):
    """Interactive Brokers应用程序类
    
    该类负责与Interactive Brokers API交互，管理股票数据和资源。
    
    主要功能：
    1. 连接和管理IB API会话
    2. 处理市场数据请求和响应
    3. 管理资源（线程池、数据缓存等）
    4. 提供声音提醒功能
    
    资源管理：
    - 使用ResourceManager统一管理所有资源
    - 支持自动资源清理
    - 提供资源状态监控
    - 支持超时控制
    
    使用方式：
    ```python
    # 推荐使用上下文管理器方式
    with IBApp() as app:
        app.connect("127.0.0.1", 7497, clientId=1)
        app.run()
        
    # 或者手动管理
    app = IBApp()
    try:
        app.connect("127.0.0.1", 7497, clientId=1)
        app.run()
    finally:
        app.cleanup()
    ```
    
    属性：
        resource_manager (ResourceManager): 资源管理器实例
        executor (ThreadPoolExecutor): 线程池执行器
        is_shutting_down (bool): 关闭标志
        _cleanup_done (bool): 清理完成标志
        sound_process (subprocess.Popen): 声音进程
        sound_lock (threading.Lock): 声音锁
        ticker_data (defaultdict): 行情数据缓存
        orderbook_data (defaultdict): 订单簿数据缓存
    """
    
    def __init__(self) -> None:
        """初始化IBApp实例
        
        初始化过程：
        1. 调用父类初始化
        2. 设置资源管理器
        3. 注册所有需要管理的资源
        4. 初始化其他属性
        
        注意：
        - 所有资源都通过resource_manager进行管理
        - 资源清理会在退出时自动进行
        """
        MyWrapper.__init__(self)
        MyClient.__init__(self, wrapper=self)
        
        # 初始化标志和锁
        self.start = False
        self.lock = threading.RLock()
        self.sound_lock = threading.RLock()
        self.is_shutting_down = False
        self._cleanup_done = False
        
        # 初始化全局请求时间控制变量
        self.last_global_tick_request_time = datetime.now(tz=NY_TZ)
        
        # 设置最小请求间隔时间（秒）
        self.TICK_REQUEST_INTERVAL = 1  # 每个股票1内只能请求一次
        
        # 初始化资源管理器
        self.resource_manager = ResourceManager(cleanup_timeout=30)
        
        # 注册线程池资源
        self.resource_manager.register(
            'thread_pool',
            ThreadPoolExecutor(max_workers=12),
            cleanup_handler=lambda x: x.shutdown(wait=True)
        )
        
        # 注册声音进程资源
        self.resource_manager.register(
            'sound_process',
            None,  # 初始为None，后续会更新
            cleanup_handler=lambda x: x.terminate() if x and x.poll() is None else None
        )
        
        # 注册数据缓存资源
        self.resource_manager.register(
            'data_cache',
            {
                'ticker_data': defaultdict(lambda: pd.DataFrame()),
                'orderbook_data': defaultdict(lambda: pd.DataFrame())
            },
            cleanup_handler=lambda x: x.clear()
        )
        
        # 注册锁资源
        self.resource_manager.register(
            'sound_lock',
            threading.Lock()
        )
        
        # 其他初始化代码保持不变
        self.executor = self.resource_manager._resources['thread_pool']
        self.is_shutting_down = False
        self._cleanup_done = False
        
        # 使用注册的资源
        self.sound_process = None  # 将在需要时更新
        self.sound_lock = self.resource_manager._resources['sound_lock']
        self.ticker_data = self.resource_manager._resources['data_cache']['ticker_data']
        self.orderbook_data = self.resource_manager._resources['data_cache']['orderbook_data']
        
        # 外部依赖
        self.quote_ctx_ = None
        
        # 性能优化参数
        self.batch_size = 10
        self.call_interval = 8
        
        # 任务追踪
        self.tracking_tasks = {}
        
        # 合约管理
        self.symbol = None
        self.contract_dict = {}

    def connect(self, host: str, port: int, clientId: int = 1) -> None:
        """连接到IB服务器"""
        super().connect(host, port, clientId)
        
    def disconnect(self) -> None:
        """断开与IB服务器的连接"""
        super().disconnect()
        self.start = False
            
    def process_stock_data(self, stock_code: str, stock_count: int, stock_count_day: int = 0) -> None:
        """异步处理股票数据"""
        if self.is_shutting_down:
            return
            
        try:
            # 更新字典
            with self.lock:
                self.reqId_dict[stock_code] = stock_count
                self.reqId_dict_day[stock_code] = stock_count_day
                self.reqId_dict_wrapper.update({
                    stock_count: stock_code,
                    stock_count_day: stock_code
                })

            # 使用键值生成器获取所有需要的键
            keys = key_generator.get_keys_for_stock(stock_code)

            # 获取新闻数据
            news_dict = get_todays_news(stock_code, self.curr_date)
            
            # 更新新闻列表
            if news_dict.get(stock_code):
                manager_res_dict_test.setdefault(keys[NEWS], []).extend(
                    item['news_content'] for item in news_dict[stock_code]
                )
            
            # 并行执行新闻和股票信息获取
            if not self.is_shutting_down:
                with ThreadPoolExecutor(max_workers=3) as local_executor:
                    futures = []
                    futures.extend([
                        local_executor.submit(get_stocktitan_news, {stock_code}, manager_res_dict_test,
                                     self.quote_ctx_, self.news_stock_dict, self.news_stock_dict_lock),
                        local_executor.submit(get_globenewswire_news, {stock_code}, manager_res_dict_test,
                                     self.quote_ctx_, self.news_stock_dict, self.news_stock_dict_lock),
                        local_executor.submit(get_stock_sharesOutstanding, stock_code)
                    ])
                    
                    # 等待所有任务完成
                    for future in as_completed(futures):
                        try:
                            future.result(timeout=10)
                        except TimeoutError:
                            logging.error(f"{stock_code} 数据请求超时", exc_info=True)
                        except Exception as e:
                            logging.error(f"{stock_code} 任务执行错误: {str(e)}", exc_info=True)
            
            # 请求历史数据和买卖价
            if not self.is_shutting_down:
                self.request_historical_data(stock_count, stock_count_day)
                self.get_bid_ask(stock_count)

            logging.info(f'异步获取股票数据结束 {stock_code}')
            
        except Exception as e:
            logging.error(f"处理股票 {stock_code} 时发生错误: {str(e)}", exc_info=True)
            
    def check_news_stock_dict(self) -> None:
        """清理过期的新闻数据"""
        now_time = int(datetime.now(tz=NY_TZ).timestamp())
        with self.news_stock_dict_lock:
            expired_keys = [
                k for k, v in self.news_stock_dict.items()
                if now_time - v > self.EXPIRE_SECONDS
            ]
            for k in expired_keys:
                del self.news_stock_dict[k]
                
    def process_queue(self) -> None:
        """处理监控队列"""
        if self.is_shutting_down:
            logging.info("process_queue: 应用正在关闭，跳过处理")
            return
        
        if not self.watch_dict:
            return
        
        try:
            # 去重并排序
            self.watch_dict = self.deduplicate_and_sort(self.watch_dict)
            
            # 更新自选股列表
            self.update_watchlist_and_modify()
            
        except Exception as e:
            logging.error(f"处理监控队列时发生错误: {str(e)}", exc_info=True)

    def request_historical_data(self, stock_count: int, stock_count_day: int) -> None:
        # 设置结束日期
        # end_date = '20240830 15:50:00 America/New_York'
        # self.reqHistoricalData(reqId=self.reqId_dict[self.symbol], contract=contract, endDateTime=end_date,
        #                   durationStr='2 D', barSizeSetting='1 min', whatToShow='TRADES',
        #                   useRTH=0, formatDate=1, keepUpToDate=False, chartOptions=[])

        # 请求历史数据
        count = 0
        req_flg = True
        while(count < 3 and req_flg):
            count += 1
            if stock_count in self.contractDetails_dict:
                # self.reqHistoricalData(reqId=reqid_ , contract=self.contractDetails_dict[reqid_], 
                #                     endDateTime=end_date, 
                #                     durationStr='2 D', barSizeSetting='1 min', whatToShow='TRADES',
                #                         useRTH=0, formatDate=1, keepUpToDate=False, chartOptions=[])
                self.reqHistoricalData(reqId=stock_count , contract=self.contractDetails_dict[stock_count], 
                                    endDateTime='', durationStr='3 D', barSizeSetting='1 min', whatToShow='TRADES',
                                        useRTH=0, formatDate=1, keepUpToDate=True, chartOptions=[])
                self.reqHistoricalData(reqId=stock_count_day , contract=self.contractDetails_dict[stock_count], 
                                    endDateTime='', durationStr='200 D', barSizeSetting='1 day', whatToShow='TRADES',
                                        useRTH=0, formatDate=1, keepUpToDate=False, chartOptions=[])
                
                # 请求逐笔数据
                if self.new_contractDetails_dict.get(stock_count) < 4:
                    self.request_tick_data(stock_count)
                
                req_flg = False
            if req_flg:
                time.sleep(2)

    def request_tick_data(self, reqid_: int) -> None:
        """统一请求逐笔交易和报价数据
        """
        
        try:
            contract = self.contractDetails_dict[reqid_]
            stock_code = contract.symbol

            now = datetime.now(tz=NY_TZ)
            
            # 全局请求频率控制 - 避免短时间内发送太多请求
            if hasattr(self, 'last_global_tick_request_time'):
                time_since_last_request = now - self.last_global_tick_request_time
                if time_since_last_request < timedelta(seconds=1):  # 至少间隔1秒
                    sleep_time = 1 - time_since_last_request.total_seconds()
                    if sleep_time > 0:
                        logging.info(f"全局请求限流，等待 {sleep_time:.2f} 秒")
                        time.sleep(sleep_time)
            
            # 更新全局最后请求时间
            self.last_global_tick_request_time = datetime.now(tz=NY_TZ)
            
            # 单个股票请求频率控制
            if stock_code in self.last_tick_request_time and now - self.last_tick_request_time[stock_code] < timedelta(seconds=self.TICK_REQUEST_INTERVAL):
                logging.info(f"股票 {stock_code} 在 {self.last_tick_request_time[stock_code]} 请求过，跳过请求")
                return
            
            if stock_code in self.inactive_tick:
                stock_count_tick = self.active_tick_requests[stock_code]
                stock_count_tick_bid_ask = self.active_BidAsk_requests[stock_code]
                self.inactive_tick.discard(stock_code)
            else:
                with self.lock:
                    # 检查活跃请求数量，避免过多并发请求
                    active_request_count = len(self.active_tick_requests)
                    if active_request_count >= 8:  # 最多同时处理8个股票的逐笔数据
                        logging.warning(f"活跃逐笔数据请求已达上限 ({active_request_count})，暂时跳过 {stock_code}")
                        return
                        
                    stock_count_tick = self.stock_count_tick
                    stock_count_tick_bid_ask = self.stock_count_tick_bid_ask
                    self.active_tick_requests[stock_code] = stock_count_tick
                    self.tick_reqId_stock_dict[stock_count_tick] = stock_code
                    self.stock_count_tick += 1
                    self.active_BidAsk_requests[stock_code] = stock_count_tick_bid_ask
                    self.bid_ask_reqId_stock_dict[stock_count_tick_bid_ask] = stock_code
                    self.stock_count_tick_bid_ask += 1

            self.last_tick_request_time[stock_code] = now
            # 发送Last类型请求
            self.reqTickByTickData(stock_count_tick, contract, "AllLast", 0, False)
            logging.info(f"请求Last类型逐笔数据结束 {stock_code} {stock_count_tick}")

            # 启动定时器，间隔后执行BidAsk请求
            self.reqTickByTickData(stock_count_tick_bid_ask, contract, "BidAsk", 0, False)
            logging.info(f"请求BidAsk类型逐笔数据结束 {stock_code} {stock_count_tick_bid_ask}")

        except Exception as e:
            logging.error(f"请求逐笔数据时发生错误: {str(e)}", exc_info=True)
            if 'stock_count_tick' in locals():
                self.cancelTickByTickData(stock_count_tick)
            if 'stock_count_tick_bid_ask' in locals():
                self.cancelTickByTickData(stock_count_tick_bid_ask)
            if 'stock_code' in locals():
                self.inactive_tick.add(stock_code) # 将股票代码添加到已取消的逐笔请求集合中

    def get_bid_ask(self, reqid_: int) -> None:
        """请求实时市场数据，包括买价和卖价"""
        count = 0
        req_flg = True
        while(count < 3 and req_flg):
            count += 1
            if reqid_ in self.contractDetails_dict:
                self.reqMktData(reqid_, self.contractDetails_dict[reqid_], "294", False, False, [])
                req_flg = False
            if req_flg:
                time.sleep(2)

    def update_watchlist_and_modify(self) -> None:
        """更新 watchlist 并调用 modify_the_watchlist"""
        watch_list = list(self.watch_dict.values())
        logging.info(f'watchlist {watch_list}')
        logging.info(f'pre_symbol_list_pre {self.pre_symbol_list_pre}')
        
        if set(watch_list) != set(self.pre_symbol_list_pre) or len(watch_list) != len(self.pre_symbol_list_pre):
            self.pre_symbol_list_pre = watch_list
            
        # 初始化
        filtered_list = []
        web_stock_lsit = []
        breakout_stocks = []
        
        for rank, stock_code in list(self.watch_dict.items()):
            # 使用键值生成器获取所有需要的键
            keys = key_generator.get_keys_for_stock(stock_code)
            
            # 获取对应的标志值
            # 获取交易次数、MACD标志、突破标志等
            number_count = self.market_data_manager.get(stock_code, NUMBER_FLG, 0)
            macd_flag = self.market_data_manager.get(stock_code, MACD_FLG, False)
            macd_5_flag = self.market_data_manager.get(stock_code, MACD_5_FLG, False)
            breakout_flg = self.market_data_manager.get(stock_code, BREAKOUT_FLG, False)
            breakout_red_flg = self.market_data_manager.get(stock_code, BREAKOUT_RED_FLG, False)
            now_price = self.market_data_manager.get(stock_code, NOW_PRICE, 0)

            # 根据股价调整交易次数
            if now_price > 4:
                number_count_flg = 200
            else:
                number_count_flg = 300
            
            # 条件判断
            if number_count > number_count_flg:
                if (macd_flag or macd_5_flag) or breakout_flg:
                    if breakout_flg or breakout_red_flg:
                        breakout_stocks.append(stock_code)
                    else:
                        filtered_list.append(stock_code)
                web_stock_lsit.append(stock_code)
                    
            logging.info(f"{stock_code} macd flg {macd_flag} macd5 flg {macd_5_flag} 交易次数 {number_count} breakout_flg {breakout_flg} breakout_red_flg {breakout_red_flg}")
        manager_res_dict_test[WEB_STOCK_LIST] = web_stock_lsit + list(self.news_stock_dict.keys())
        # 合并结果（突破股在前）
        final_list = breakout_stocks + filtered_list + list(self.news_stock_dict.keys())
        
        # 有序去重
        final_list = list(dict.fromkeys(final_list))
        logging.info(f'filtered_list {final_list} filtered_list_backup {self.filtered_list_backup} news_stock_dict {list(self.news_stock_dict.keys())}')
        logging.info('')
        
        # 判断是否需要更新
        if final_list != self.filtered_list_backup:
            self.filtered_list_backup = final_list
            manager_res_dict[1] = final_list
            modify_the_watchlist(final_list, self.quote_ctx_)

            # 新增：推送股票列表到WebSocket前端
            # try:
            #     sync_push_stock_list_to_frontend(final_list)
            # except Exception as e:
            #     logging.error(f"推送股票列表失败: {e}")
                # 不影响主流程，继续执行
            
    def scannerData(self, reqId: int, rank: int, contractDetails: ContractDetails, distance: str, benchmark: str, projection: str, legsStr: str) -> None:
        """处理市场扫描仪返回的数据"""
        stock_code = contractDetails.contract.localSymbol
        logging.info('')
        logging.info(f'----------scannerData----------   {stock_code}  {rank} ----------')
        
        # 过滤无效股票代码
        if ' ' in stock_code:
            return
            
        # 过滤低优先级数据
        if rank > 9:
            return

        # 加锁保护共享数据
        with self.lock:
            # 添加到监控列表
            self.watch_dict[rank] = stock_code
            
            # 取消 rank > 3 的股票的逐笔数据请求
            if rank > 3 and stock_code in self.active_tick_requests and stock_code not in self.inactive_tick:
                self.cancelTickByTickData(self.active_tick_requests[stock_code])
                self.cancelTickByTickData(self.active_BidAsk_requests[stock_code])
                self.inactive_tick.add(stock_code)
                self.market_data_manager.set(stock_code, SIGNAL_STRENGTH, REMOVED_FROM_TOP5)
                logging.info(f'跌出前4名 取消逐笔数据请求 {stock_code}')
            elif rank < 4 and (stock_code in self.inactive_tick\
                or (stock_code not in self.active_tick_requests and stock_code in self.reqId_dict)):
                logging.info(f'进入前4名 重新请求逐笔数据 {stock_code}')
                self.request_tick_data(self.reqId_dict[stock_code])
            # 处理新股票
            if stock_code not in self.tracking_tasks and stock_code not in self.reqId_dict:
                # print(f'合同详情 {reqId} {contractDetails.contract.symbol} {contractDetails.contract.primaryExchange} {contractDetails.tradingHours} {contractDetails.liquidHours}')
                # 添加到股票集合
                self.symbol_set.add(stock_code)
                self.stock_count += 1
                self.stock_count_day += 1
                self.new_contractDetails_dict[self.stock_count] = rank
                self.contractDetails_dict[self.stock_count] = contractDetails.contract
                future = self.executor.submit(
                    self.process_stock_data,
                    stock_code,
                    self.stock_count,
                    self.stock_count_day
                )
                self.tracking_tasks[stock_code] = future

    def create_contract(self, stock_code: str) -> None:
        """创建合约实例"""
        contract = Contract()
        contract.symbol = stock_code  # 股票代码
        contract.secType = "STK"  # 证券类型（股票）
        contract.exchange = "SMART"  # 交易所
        contract.currency = "USD"  # 货币
        self.contract_dict[self.reqId_dict[stock_code]] = contract

    def get_ContractDetails(self, stock_code: str) -> None:
        """获取合同详情"""
        self.reqContractDetails(self.reqId_dict[stock_code], self.contract_dict[self.reqId_dict[stock_code]])

    def request_pre_market_top_gainers(self) -> None:
        """请求涨幅最大的股票
        
        该方法用于获取市场中涨幅最大的股票。
        
        参数说明：
        - numberOfRows: 返回的股票数量
        - instrument: 交易品种，STK表示股票
        - locationCode: 交易所位置，STK.US.MAJOR表示美国主要交易所
        - scanCode: 扫描代码，TOP_PERC_GAIN表示按涨幅排序
        - stockTypeFilter: 股票类型过滤器，CORP表示仅普通股
        - abovePrice: 最低价格限制
        - belowPrice: 最高价格限制
        - aboveVolume: 最低成交量限制
        """
        scan_sub = ScannerSubscription()
        scan_sub.numberOfRows = 30
        scan_sub.instrument = "STK"
        scan_sub.locationCode = "STK.US.MAJOR"
        scan_sub.scanCode = "TOP_PERC_GAIN"
        scan_sub.stockTypeFilter = "CORP"  # 仅限普通股票（排除ETF）
        # 价格和成交量过滤条件（可选）
        # scan_sub.abovePrice = 0.13  # 股价下限
        # scan_sub.belowPrice = 30    # 股价上限
        # scan_sub.aboveVolume = 20000  # 最小成交量
        
        self.reqScannerSubscription(7001, scan_sub, "", [])

    def __enter__(self):
        """进入上下文管理器
        
        该方法使得IBApp可以在with语句中使用，提供了自动资源管理。
        
        返回：
            self: 当前IBApp实例
            
        示例：
        ```python
        with IBApp() as app:
            app.connect("127.0.0.1", 7497, clientId=1)
            app.run()
        ```
        """
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文管理器
        
        该方法在with语句块结束时自动调用，确保资源被正确清理。
        
        参数：
            exc_type: 异常类型（如果发生异常）
            exc_val: 异常值（如果发生异常）
            exc_tb: 异常回溯（如果发生异常）
            
        注意：
        - 即使发生异常，也会尝试清理资源
        - 不会吞掉异常，异常会继续向上传播
        """
        self.cleanup()
        
    def cleanup(self) -> bool:
        """清理所有资源
        
        该方法负责清理所有注册的资源，包括：
        1. 线程池
        2. 声音进程
        3. 数据缓存
        4. 其他资源
        
        特性：
        - 线程安全
        - 防止重复清理
        - 支持超时控制
        - 提供清理状态追踪
        
        异常：
            Exception: 清理过程中发生的任何异常
        """
        try:
            # 如果已经在清理或已经清理完毕，直接返回
            if self._cleanup_done or self.is_shutting_down:
                logging.info("资源已经在清理中或已清理完毕，跳过重复清理")
                return
                
            # 设置关闭标志
            self.is_shutting_down = True
            
            # 使用资源管理器清理所有资源
            cleanup_results = self.resource_manager.cleanup_all()
            
            # 检查清理结果
            for resource_id, success in cleanup_results.items():
                if not success:
                    logging.error(f"资源 {resource_id} 清理失败")
                    
            # 断开连接
            self.disconnect()
            
            # 标记清理完成
            self._cleanup_done = True
            logging.info("资源清理完成")
            
        except Exception as e:
            logging.error(f"清理资源时发生错误: {str(e)}", exc_info=True)
            raise

    def get_resource_status(self) -> dict[str, Any]:
        """获取所有资源的状态
        
        返回所有受管理资源的详细状态信息。
        
        返回：
            Dict[str, Any]: 包含每个资源的状态信息的字典
            {
                'resource_id': {
                    'resource': 资源对象,
                    'status': 资源状态,
                    'start_time': 创建时间,
                    'age': 存活时间（秒）
                }
            }
            
        异常：
            Exception: 获取状态时发生的错误会被记录但不会抛出
        """
        status = {}
        for resource_id in self.resource_manager._resources.keys():
            try:
                status[resource_id] = self.resource_manager.get_resource_info(resource_id)
            except Exception as e:
                logging.error(f"获取资源 {resource_id} 状态时发生错误: {str(e)}")
                status[resource_id] = {'status': 'ERROR', 'error': str(e)}
        return status

def dataDataframe(user_choice_stocks_list: list[str]) -> dict[str, pd.DataFrame]:
    """生成历史数据框架
    
    将原始数据转换为DataFrame格式，并进行时间处理。
    
    Args:
        user_choice_stocks_list: 股票代码列表
        
    Returns:
        dict: 股票代码到DataFrame的映射
    """
    df_data = {}
    # 预获取常用对象引用，减少字典查询次数
    reqId_dict = ib_app.reqId_dict
    app_data = ib_app.data
    
    for symbol in user_choice_stocks_list:
        reqId = reqId_dict.get(symbol)
        if reqId is None or reqId not in app_data:
            continue
            
        # 批量数据转换替代逐行处理
        raw_data = list(app_data[reqId].values())
        if not raw_data:
            continue
            
        # 使用向量化操作替代apply
        df = pd.DataFrame(raw_data)
        
        # 使用优化后的时间处理
        # "20250420 08:12:00 US/Eastern"
        df['date_time'] = pd.to_datetime(
            df['date_time'].str[:-11],  # 向量化字符串操作
            format='%Y%m%d %H:%M:%S',
            exact=False
        ) + pd.Timedelta(minutes=1)
        
        # 使用更高效的内存布局
        df = df.set_index("date_time", inplace=False).sort_index()
        df_data[symbol] = df
        
    return df_data

def screener(trading_session: str) -> None:
    """股票筛选器
    
    根据不同的交易时段筛选股票。
    
    Args:
        trading_session: 交易时段（'premarket_change'或'postmarket_change'）
    """
    if not ib_app or not ib_app.start:
        return
        
    stock_list = tradingview_screener_stock(trading_session)
    logging.info(f'tradingview_screener_stock: {stock_list}')
    
    for stock_code in stock_list:
        # 基本过滤
        if ' ' in stock_code:
            continue

        rank = stock_list.index(stock_code)
        keys = key_generator.get_keys_for_stock(stock_code)

        # 加锁保护共享数据
        with ib_app.lock:
            # 取消 rank > 3 的股票的逐笔数据请求
            if rank > 3 and stock_code in ib_app.active_tick_requests and stock_code not in ib_app.inactive_tick:
                ib_app.cancelTickByTickData(ib_app.active_tick_requests[stock_code])
                ib_app.cancelTickByTickData(ib_app.active_BidAsk_requests[stock_code])
                ib_app.inactive_tick.add(stock_code)
                ib_app.market_data_manager.set(stock_code, SIGNAL_STRENGTH, REMOVED_FROM_TOP5)
                logging.info(f'跌出前4名 取消逐笔数据请求 {stock_code}')
            elif rank < 4 and (stock_code in ib_app.inactive_tick\
                or (stock_code not in ib_app.active_tick_requests and stock_code in ib_app.reqId_dict)):
                logging.info(f'进入前4名 重新请求逐笔数据 {stock_code}')
                ib_app.request_tick_data(ib_app.reqId_dict[stock_code])
            # 确保任务不重复
            if (stock_code.isalpha() and 
                stock_code not in ib_app.tracking_tasks and 
                stock_code not in ib_app.reqId_dict):
                # 添加到股票集合
                ib_app.symbol_set.add(stock_code)
                # 更新计数器
                ib_app.stock_count += 1
                ib_app.stock_count_day += 1
                # 更新字典
                ib_app.new_contractDetails_dict[ib_app.stock_count] = stock_list.index(stock_code)
                ib_app.reqId_dict[stock_code] = ib_app.stock_count
                # 创建合约实例
                ib_app.create_contract(stock_code)
                # 获取合同详情
                ib_app.get_ContractDetails(stock_code)
                # 提交处理任务
                future = ib_app.executor.submit(
                    ib_app.process_stock_data,
                    stock_code,
                    ib_app.stock_count,
                    ib_app.stock_count_day
                )
                ib_app.tracking_tasks[stock_code] = future
            # 添加到监控列表
            ib_app.watch_dict[rank] = stock_code

count_ = 0
historicalData = {}
signal_time = {}

ib_app = None
def init_ib_app(quote_ctx: FutuQuoteContext, sound_warn: str, web_stock_data: dict = None) -> IBApp:
    """初始化IB应用程序
    
    创建并初始化IB应用程序实例，设置连接参数并启动。
    
    Args:
        quote_ctx: 行情上下文
        sound_warn: 警告文件路径
        
    Returns:
        IBApp: 初始化后的IB应用程序实例
    """
    global ib_app
    ib_app = IBApp()
    
    # 使用配置文件中的设置
    for _ in range(IB_CONFIG['MAX_RETRY_ATTEMPTS']):
        try:
            ib_app.connect(
                host=IB_CONFIG['HOST'],
                port=IB_CONFIG['PORT'],
                clientId=IB_CONFIG['CLIENT_ID']
            )
            
            # 启动事件循环线程
            con_thread = threading.Thread(target=ib_app.run, daemon=True)
            con_thread.start()
            
            # 等待连接建立
            timeout = 20
            waiting_time = 0.5
            start_time = time.time()
            
            while not ib_app.start:
                if time.time() - start_time > timeout:
                    raise TimeoutError("等待nextValidId回调超时")
                if not ib_app.isConnected():
                    raise ConnectionError("连接已断开")
                time.sleep(waiting_time)
            
            logging.info("成功接收到nextValidId回调，连接已就绪")

            # 检查服务器版本和连接时间
            server_version = ib_app.serverVersion()
            connection_time = ib_app.twsConnectionTime()
            logging.info(f'服务器版本: {server_version}')
            logging.info(f'连接时间: {connection_time}')

            # 推送完整的IB API连接状态
            # 暂时注释掉共享内存相关调用
            # try:
            #     print("=== 开始推送完整IB API连接状态 ===", flush=True)
            #     push_ib_api_status_to_frontend(
            #         is_connected=True,
            #         server_version=str(server_version),
            #         connection_time=str(connection_time),
            #         active_stocks_count=get_active_stock_count()
            #     )
            #     print("=== 完整IB API连接状态推送调用完成 ===", flush=True)
            #     logging.info("IB API连接状态推送成功")
            # except Exception as e:
            #     print(f"=== 推送完整IB API连接状态失败: {e} ===", flush=True)
            #     logging.error(f"推送完整IB API连接状态失败: {e}")

            # 设置上下文
            ib_app.quote_ctx_ = quote_ctx
            ib_app.sound_warn = sound_warn
            ib_app.web_stock_data = web_stock_data if web_stock_data is not None else {}
            
            # 根据时间订阅市场扫描仪
            now_ = datetime.now(tz=NY_TZ)
            if now_.hour < 16:  # 盘中
                logging.info('订阅盘中市场扫描仪')
                ib_app.request_pre_market_top_gainers()
            
            break
            
        except Exception as e:
            logging.error(f"连接失败，尝试重新连接: {e}", exc_info=True)
            time.sleep(IB_CONFIG['RETRY_INTERVAL'])
    
    if not ib_app.isConnected():
        raise ConnectionError("无法连接到IB Gateway")
    
    return ib_app

def _merge_dataframe_with_uuid(buffer_data, existing_data):
    """
    合并带UUID的DataFrame数据的通用方法
    
    Args:
        buffer_data: 缓冲区数据列表
        existing_data: 现有的DataFrame数据
        
    Returns:
        pd.DataFrame: 合并后的DataFrame
    """
    if not buffer_data:
        return existing_data
        
    # 直接从缓冲区创建DataFrame，无需重新生成UUID
    new_df = pd.DataFrame(buffer_data)
    
    if existing_data.empty:
        # 如果现有数据为空，直接设置索引
        return new_df.set_index('uuid')
    else:
        # 合并数据
        if existing_data.index.name == 'uuid':
            # 如果已有索引，重置为普通列
            history_data = existing_data.reset_index()
        else:
            history_data = existing_data.reset_index(drop=True)
        
        # 合并并设置索引
        return pd.concat([history_data, new_df]).set_index('uuid')

# 初始化退出标志
exit_flag = False
def main(quote_ctx: FutuQuoteContext, sim_trd_ctx: FutuQuoteContext, sound_warn: str, sound_signal: str, web_stock_data: dict = None) -> None:
    """主函数
    
    Args:
        quote_ctx: 富途API上下文
        sim_trd_ctx: 模拟交易上下文
        sound_warn: 声音警告文件路径
        sound_signal: 声音信号文件路径
    """
    try:
        # 初始化IB应用
        global ib_app
        init_ib_app(quote_ctx, sound_warn, web_stock_data)
        
        # 注册清理函数
        def cleanup_ib_app():
            global ib_app
            if ib_app and not getattr(ib_app, '_cleanup_done', False):
                ib_app.cleanup()
                
        import atexit
        atexit.register(cleanup_ib_app)
        
        # 设置定时任务
        def set_exit_flag():
            """设置退出标志"""
            global exit_flag
            exit_flag = True
            
            # 清除所有定时任务
            schedule.clear()
            logging.info('--------------------------------------------退出程序--------------------------------------------')


        def clear_data():
            """清理数据，防止数据无限增长导致内存和CPU占用过高。只保留最近15分钟的数据。"""
            now = datetime.now(tz=NY_TZ)
            cutoff_time = now - timedelta(minutes=15)
            # 将cutoff_time转换为不带时区的datetime，以便与DataFrame中的datetime64进行比较
            cutoff_time_naive = cutoff_time.replace(tzinfo=None)

            with ib_app.lock:
                # 清理ticker数据
                clean_expired_data(ib_app.ticker_data, cutoff_time_naive, "ticker")
                # 清理orderbook数据
                clean_expired_data(ib_app.orderbook_data, cutoff_time_naive, "orderbook")

        def analyze_realtime_data():
            """分析实时股票数据
            
            使用stock_analyzer.py中的analyze_stock_data函数分析数据
            """
            global data_cache
            
            if not ib_app or not ib_app.start:
                logging.info("IB应用未启动或未连接，跳过分析")
                return
                
            try:
                # 记录开始时间，用于性能分析
                start_time_perf = time.time()
                
                # 对前5名股票进行分析
                for stock_code in list(ib_app.active_tick_requests.keys()):  # 使用列表复制避免迭代时修改
                    # 提前初始化keys变量，确保异常处理中可以使用
                    keys = key_generator.get_keys_for_stock(stock_code)
                    
                    try:
                        if stock_code in ib_app.inactive_tick:
                            # 如果股票代码在inactive_tick中，则跳过
                            continue
                    
                        # 创建数据
                        create_data_start = time.time()
                        with ib_app.lock:
                            tick_buffer = ib_app.tick_all_last_buffer[stock_code].copy()
                            orderbook_buffer = ib_app.tick_bid_ask_buffer[stock_code].copy()
                            
                            # 合并缓冲区数据到现有DataFrame
                            ib_app.ticker_data[stock_code] = _merge_dataframe_with_uuid(
                                tick_buffer, ib_app.ticker_data[stock_code]
                            )
                            ib_app.orderbook_data[stock_code] = _merge_dataframe_with_uuid(
                                orderbook_buffer, ib_app.orderbook_data[stock_code]
                            )

                            # 更新数据长度
                            tick_data_len = len(ib_app.ticker_data[stock_code])
                            orderbook_data_len = len(ib_app.orderbook_data[stock_code])
                            tick_all_last_buffer_len = len(ib_app.tick_all_last_buffer[stock_code])
                            tick_bid_ask_buffer_len = len(ib_app.tick_bid_ask_buffer[stock_code])
                            
                            # 检查数据量是否足够
                            if tick_data_len < 1000 or orderbook_data_len < 1000:
                                logging.info(f"股票 {stock_code} Ticker缓冲区数据点: {tick_all_last_buffer_len}, Orderbook缓冲区数据点: {tick_bid_ask_buffer_len}")
                                logging.info(f"股票 {stock_code} 数据量不足，跳过分析 Ticker数据点: {tick_data_len}, Orderbook数据点: {orderbook_data_len}")
                                # 清空缓冲区
                                ib_app.tick_all_last_buffer[stock_code].clear()
                                ib_app.tick_bid_ask_buffer[stock_code].clear()
                                continue
                            
                            # 清空缓冲区
                            ib_app.tick_all_last_buffer[stock_code].clear()
                            ib_app.tick_bid_ask_buffer[stock_code].clear()
                        
                        logging.info(f'\n')
                        logging.info(f"股票 {stock_code} Ticker缓冲区数据点: {tick_all_last_buffer_len}, Orderbook缓冲区数据点: {tick_bid_ask_buffer_len}")
                        logging.info(f"股票 {stock_code} 准备分析数据 {stock_code} - Ticker数据点: {tick_data_len}, Orderbook数据点: {orderbook_data_len}")
                        logging.info(f"股票 {stock_code} 用时: {time.time() - create_data_start:.4f}秒")
                        
                        # 获取当前价格作为基准价格
                        current_price = ib_app.ticker_data[stock_code]['price'].iloc[-1] if not ib_app.ticker_data[stock_code].empty else 0

                        # 获取当前纽约时间，但去掉时区信息
                        now = datetime.now(tz=NY_TZ).replace(tzinfo=None)
                        
                        # 格式化时间字符串用于缓存键
                        window_end_str = now.strftime('%Y-%m-%d_%H_%M_%S')
                        
                        # 使用股票代码和时间戳组合作为缓存键
                        cache_key = (stock_code, window_end_str)

                        # 缓存数据以便后续检查价格变化
                        with data_cache_lock:
                            # 创建一个新的分析历史记录列表
                            if cache_key not in data_cache:
                                data_cache[cache_key] = {
                                    'timestamp': window_end_str,  # 使用字符串格式的时间戳
                                    'ticker_data': ib_app.ticker_data[stock_code].copy(),
                                    'orderbook_data': ib_app.orderbook_data[stock_code].copy(),
                                    'base_price': current_price
                                    # 'analysis_results': analysis_results,  # 当前分析结果
                                }
                    
                        # 记录分析开始时间
                        analyzer_start_time = time.time()

                        # 调用分析函数
                        results = analyzer_analyze_stock_data(
                            ticker_data=ib_app.ticker_data[stock_code],
                            orderbook_data=ib_app.orderbook_data[stock_code],
                            analysis_time=now,  # 传入当前时间用于精确确定分析窗口
                            last_n_windows=2    # 使用配置的窗口数量
                        )
                        analyzer_time = time.time() - analyzer_start_time
                        logging.info(f"股票 {stock_code} 分析函数耗时: {analyzer_time:.4f}秒")
                        
                        # 处理分析结果
                        # 使用统一的函数处理分析结果
                        analysis_results = create_analysis_results_from_analyzer(results, current_price)
                        signal_strength = analysis_results['signal_strength']
                        
                        # 安全地获取倒数第二个窗口的信息
                        prev_window_info = ""
                        if isinstance(results, list) and len(results) > 1:
                            prev_window_info = f'倒数第二个窗口:{results[-2]["time"]} {results[-2]["signal_strength"]}'
                        logging.info(f'{stock_code} 信号强度: {signal_strength} {prev_window_info}\n'+
                                        format_analysis_results(analysis_results))
                        
                        # 更新10秒窗口分析结果
                        update_manager_res_dict_test(manager_res_dict_test, keys, analysis_results)
                    
                    except Exception as e:
                        # 处理单个股票的错误，但继续处理其他股票
                        logging.error(f"处理股票 {stock_code} 时出错: {str(e)}", exc_info=True)
                        # 确保在发生错误时也设置默认值
                        set_manager_res_dict_test_default(manager_res_dict_test, keys)
                
                total_time = time.time() - start_time_perf
                logging.info(f'分析完成所有股票 - 总耗时: {total_time:.4f}秒')
                    
            except Exception as e:
                # 处理整个分析过程的错误
                logging.error(f"分析实时数据时出错: {str(e)}", exc_info=True)
            finally:
                total_time = time.time() - start_time_perf
                logging.info(f"analyze_realtime_data 总耗时: {total_time:.4f}秒")

        def check_price_changes():
            """检查缓存数据中的价格变化，保存或删除数据"""
            global data_cache
            # 获取纽约时间但去掉时区信息
            now = datetime.now(tz=NY_TZ).replace(tzinfo=None)
            to_day = now.strftime('%Y-%m-%d')
            
            # 确保缓存目录存在
            if not os.path.exists(f'{DATA_CACHE_DIR}/{to_day}'):
                os.makedirs(f'{DATA_CACHE_DIR}/{to_day}')
            
            with data_cache_lock:
                # 获取需要处理的缓存键列表（复制以避免在迭代中修改字典）
                data_cache_copy = data_cache.copy()
                for cache_key, cache_entry in data_cache_copy.items():
                    handle_price_change_and_cache(cache_key, cache_entry, now, to_day, manager_res_dict_test, key_generator, data_cache)

        def check():
            """检查股票状态
            
            每4秒执行一次，检查股票的各项指标
            """
            logging.info(f'check list: {ib_app.pre_symbol_list_pre}')
            historicalData = dataDataframe(ib_app.pre_symbol_list_pre)
            
            # 批量处理股票数据
            items = []
            for stock_code, df in historicalData.items():
                # 使用键值生成器获取所有需要的键
                keys = key_generator.get_keys_for_stock(stock_code)

                check_breakout_red(stock_code, df, manager_res_dict_test)
                check_breakout(stock_code, df, manager_res_dict_test)
                macd_signal(stock_code, df, manager_res_dict_test)
                
                value = ib_app.market_data_manager.get(stock_code, NUMBER_FLG)
                items.append((stock_code, value))
            # 返回每分钟交易次数排名
            check_number_of_transactions(items, number_of_transactions_dict)
            logging.info(f'check list end')

        def check_watch_list():
            """更新自选股票列表
            
            每8秒执行一次
            """
            if ib_app and hasattr(ib_app, 'executor') and ib_app.executor:
                ib_app.executor.submit(ib_app.process_queue)
            else:
                logging.info("无法执行check_watch_list: ib_app或executor已被清理")

        def scheduled_job():
            """定时任务
            
            每10秒执行一次，处理盘后数据
            """
            if ib_app and not ib_app.is_shutting_down:
                now_ = datetime.now(tz=NY_TZ)
                # if now_.hour < 10:
                #     screener('premarket_change')  # 盘前
                if now_.hour > 15:
                    screener('postmarket_change')  # 盘后
            else:
                logging.info("无法执行scheduled_job: ib_app不存在或正在关闭")

        def restart_app():
            """重启应用
            
            每分钟执行一次，检查并重启应用
            """
            global ib_app
            if not ib_app or not ib_app.start or not ib_app.isConnected():
                # 非阻塞方式唤醒Mac（防止睡眠）
                subprocess.Popen(["caffeinate", "-t", str(5 * 60)])
                logging.info('--------------------------------------------重启ib gateway--------------------------------------------')
                if ib_app:
                    ib_app.cleanup()
                restart_tws()
                logging.info('--------------------------------------------重启ib app--------------------------------------------')
                # 修复：重启时传递web_stock_data参数，确保Web数据更新不中断
                init_ib_app(quote_ctx, sound_warn, web_stock_data)

        def get_news():
            """获取新闻
            
            每分钟执行两次，获取最新新闻
            """
            if ib_app and hasattr(ib_app, 'executor') and ib_app.executor and not ib_app.is_shutting_down:
                ib_app.executor.submit(get_stocktitan_news, ib_app.symbol_set, manager_res_dict,
                                    quote_ctx, ib_app.news_stock_dict, ib_app.news_stock_dict_lock)
                ib_app.executor.submit(get_globenewswire_news, ib_app.symbol_set, manager_res_dict,
                                    quote_ctx, ib_app.news_stock_dict, ib_app.news_stock_dict_lock)
                ib_app.executor.submit(ib_app.check_news_stock_dict)
            else:
                logging.info("无法执行get_news: ib_app或executor已被清理")

        def job():
            """主要任务
            
            每分钟执行一次，处理主要业务逻辑
            """
            logging.info('--------------------------------------------job开始--------------------------------------------')
            now_ = datetime.now(tz=NY_TZ)
            
            if ib_app and ib_app.start and not ib_app.is_shutting_down:  # 确认建立连接后执行
                global count_, historicalData, signal_time
                
                # 检查订阅数量限制
                if len(ib_app.symbol_set) > MAX_SYMBOLS:
                    logging.info(f'当前已订阅符号数量: {len(ib_app.symbol_set)}，超出限制 {MAX_SYMBOLS}')
                    logging.info('--------------------------------------------重启ib app--------------------------------------------')
                    ib_app.cleanup()
                    time.sleep(4)
                    init_ib_app(quote_ctx, sound_warn, web_stock_data)
                    
                # 盘前盘后处理
                if now_.hour < 9 or (now_.hour == 9 and now_.minute < 20) or now_.hour > 15:
                    logging.info(f'盘前盘后涨幅TOP10股票: {ib_app.pre_symbol_list_pre}')
                    ib_app.pre_historicalData = dataDataframe(ib_app.pre_symbol_list_pre)
                    check_stock_volume(ib_app.pre_historicalData, quote_ctx, ib_app.curr_date,
                                    signal_time, manager_res_dict_test)
                                    
                # 盘中处理
                if (now_.hour > 9 or (now_.hour == 9 and now_.minute > 29)) and now_.hour < 14:
                    logging.info(f'涨幅TOP10股票: {ib_app.pre_symbol_list_pre}')
                    user_choice_stocks_list_circulatingShares = []
                    stock_transaction_records_df = pd.DataFrame()
                    stock_transaction_records_df = set_history_order_list_res(ib_app.pre_symbol_list_pre)
                    
                    historicalData = dataDataframe(ib_app.pre_symbol_list_pre)
                    main_(historicalData, stock_transaction_records_df, quote_ctx, sim_trd_ctx,
                          user_choice_stocks_list_circulatingShares, signal_time,
                          manager_res_dict_test, sound_signal)
            else:
                logging.info("无法执行job: ib_app不存在或未连接或正在关闭")

        # 设置定时任务
        schedule.every().day.at("14:00", NY_TZ).do(set_exit_flag) #14:00退出程序
        schedule.every(4).seconds.do(check) # 4秒检查一次
        schedule.every(8).seconds.do(check_watch_list) # 8秒检查一次
        schedule.every(10).seconds.do(scheduled_job) # 10秒检查一次
        schedule.every().minutes.at(':25').do(restart_app) # 每分25秒检查链接一次
        schedule.every().minutes.at(':15').do(get_news) # 每分15秒获取一次新闻
        schedule.every().minutes.at(':55').do(get_news) # 每分55秒获取一次新闻
        schedule.every().minutes.at(':38').do(job) # 每分38秒执行一次
        
        # 每10秒分析一次股票数据
        for second in ['02', '12', '22', '32', '42', '52']:
            schedule.every().minute.at(f":{second}").do(analyze_realtime_data)
        schedule.every(30).seconds.do(check_price_changes) # 每30秒检查一次价格变化
        schedule.every(5).minutes.do(clear_data) # 每5分钟清除一次数据

        # 添加多个触发器（每分钟的特定秒数）
        # for second in [1, 11, 21, 31, 41, 51]:
        #     blocking_scheduler.add_job(job, 'cron', second=second)

        # blocking_scheduler.start()

        # 主循环
        print(f"=== [IBAPI] 进入主循环，时间: {time.time()} ===", flush=True)
        while not exit_flag:
            # print(f"=== [IBAPI] 主循环心跳，时间: {time.time()} ===", flush=True)
            schedule.run_pending()
            # print(f"=== [IBAPI] schedule.run_pending()完成，时间: {time.time()} ===", flush=True)
            time.sleep(1)
            
    except Exception as e:
        logging.error(f"主函数执行错误: {str(e)}", exc_info=True)
        raise
    finally:
        # 检查是否已经清理过，避免重复清理
        if ib_app and not getattr(ib_app, '_cleanup_done', False):
            ib_app.cleanup()

def set_history_order_list_res(user_choice_stocks_list: list[str]) -> pd.DataFrame:
    # 获取当前时间
    current_time = datetime.now(tz=NY_TZ)
    start_date = current_time.strftime('%Y-%m-%d')
    stock_transaction_records_df = pd.DataFrame()
    try:
        # 读取当天股票交易到 DataFrame
        stock_transaction_records_df = pd.read_csv('/Users/<USER>/seach_stcok/stock_transaction_records/' + start_date + '.csv', header=None)
        # 添加标题
        stock_transaction_records_df.columns = ['order_id', 'date_time', 'stock_code', 'qty', 'price', 'stop_limit_price', 'sell', 'profit']
        # 将 order_id 列设置为索引
        stock_transaction_records_df.set_index('order_id', inplace=True)
    except pd.errors.EmptyDataError:
        # print("当天股票交易为空")
        logging.info("当天股票交易为空")
    except (IOError, FileNotFoundError) as e:
        # 处理文件读取异常情况
        # print(f"无法读取文件: {e}")
        logging.info(f"无法读取文件: {e}")

    if not stock_transaction_records_df.empty:
        stock_code_low_values = stock_transaction_records_df['stock_code'].tolist()
        # 使用列表推导式创建一个包含 stock_code_low_values 中的值的字典，然后将其与现有字典 user_choice_stocks_map 合并
        # user_choice_stocks_map.update({value: None for value in stock_code_low_values if value not in user_choice_stocks_map})
        # 使用列表推导式添加不重复的元素
        user_choice_stocks_list.extend([item for item in stock_code_low_values if item not in user_choice_stocks_list])
    return stock_transaction_records_df


def test_main():
    try:
        # 确保日志目录存在
        log_path = Path(LOG_CONFIG['handlers']['file']['filename']).parent
        log_path.mkdir(parents=True, exist_ok=True)
        
        # 配置日志系统
        logging.config.dictConfig(LOG_CONFIG)
        
        # 记录初始化信息
        logging.info('日志系统初始化完成')
        logging.info(f'日志文件路径: {LOG_CONFIG["handlers"]["file"]["filename"]}')

        # 真实账户
        quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
        # 模拟账号
        sim_trd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.US, host='127.0.0.1', port=11111, security_firm=SecurityFirm.FUTUSECURITIES)

        manager_res_dict[0] = 1
        manager_res_dict[1] = []

        # 启动CPU监控线程（修复计算方式）
        def monitor_ib_api_cpu():
            """监控IB API服务的CPU使用率"""
            process = psutil.Process()
            # 初始化CPU监控
            process.cpu_percent()  # 第一次调用，初始化
            time.sleep(1)

            while True:
                try:
                    # 使用更准确的CPU监控方式
                    cpu_percent = process.cpu_percent(interval=None)  # 不使用interval参数
                    memory_info = process.memory_info()
                    memory_mb = memory_info.rss / 1024 / 1024

                    # 降低警告阈值，因为活动监视器显示实际使用率很低
                    # if cpu_percent > 20:  # CPU使用率超过20%时记录
                    print(f"=== IB API服务CPU监控: CPU={cpu_percent:.1f}%, 内存={memory_mb:.1f}MB ===", flush=True)
                    logging.info(f"IB API服务CPU使用率: {cpu_percent:.1f}%, 内存: {memory_mb:.1f}MB")

                    time.sleep(10)  # 每10秒检查一次，减少监控频率
                except Exception as e:
                    logging.error(f"IB API CPU监控失败: {e}")
                    time.sleep(15)

        cpu_monitor_thread = threading.Thread(target=monitor_ib_api_cpu, daemon=True)
        cpu_monitor_thread.start()
        # print("=== IB API服务CPU监控已启动 ===", flush=True)

        sound_warn = '/Users/<USER>/git/stock_xxx/src/resources/sounds/noise.mp3'
        sound_signal = '/Users/<USER>/git/stock_xxx/src/resources/sounds/signal.wav'

        # 初始化Web股票数据字典
        web_stock_data = {}
        
        import random
        def test_push_stock_list():
            """测试推送股票列表到前端"""
            global ib_app
            while True:
                if not ib_app or not ib_app.start:
                    print("IB应用未启动或未连接，等待连接...")
                    time.sleep(5)  # 等待5秒后重试
                    continue
                if hasattr(ib_app, 'symbol_set') and ib_app.symbol_set:
                    manager_res_dict_test[WEB_STOCK_LIST] = list(ib_app.symbol_set)
                    # manager_res_dict_test[WEB_STOCK_LIST] = list(ib_app.symbol_set)[:10]
                    # manager_res_dict_test[WEB_STOCK_LIST] = random.sample(list(ib_app.symbol_set), 5)
                time.sleep(10)
        
        threading.Thread(target=test_push_stock_list, args=(), daemon=True).start()

        # 启动Web服务器线程
        if WEB_CONFIG['ENABLED']:
            try:
                from src.web.web_server import start_web_server_thread
                start_web_server_thread(
                    host=WEB_CONFIG['HOST'],
                    port=WEB_CONFIG['PORT'],
                    debug=WEB_CONFIG['DEBUG'],
                    web_stock_data=web_stock_data,
                    enable_websocket=True  # 启用WebSocket支持
                )
                logging.info(f"Web服务器已启动: http://{WEB_CONFIG['HOST']}:{WEB_CONFIG['PORT']}")
                print(f"Web服务器已启动: http://localhost:{WEB_CONFIG['PORT']}")
            except Exception as e:
                logging.error(f"Web服务器启动失败: {e}")
                print(f"Web服务器启动失败: {e}")
        else:
            logging.info("Web服务器已禁用")

        from src.utils.npm_integration import setup_npm_integration, cleanup_npm_integration
        
        # 启动NPM集成 (Vite开发服务器)
        print(f"=== [Main] 准备启动NPM集成，时间: {time.time()} ===", flush=True)
        try:
            npm_integration = setup_npm_integration()
            print(f"=== [Main] NPM集成启动完成，时间: {time.time()} ===", flush=True)
        except Exception as e:
            logging.error(f"NPM集成启动失败: {e}")
            print(f"NPM集成启动失败: {e}")

        # 启动主程序
        main(quote_ctx, sim_trd_ctx, sound_warn, sound_signal, web_stock_data)

        print('--------------------------------------------退出程序--------------------------------------------')
        sys.exit(0)
    finally:
        quote_ctx.close()
        sim_trd_ctx.close()
        # 清理NPM集成
        try:
            cleanup_npm_integration()
        except Exception as e:
            logging.error(f"清理NPM集成时出错: {e}")
        print("Active threads:", threading.enumerate())

if __name__ == "__main__":
    test_main()
