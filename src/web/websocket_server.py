"""
WebSocket服务器模块 - 提供实时股票数据推送
支持与现有REST API完全兼容的数据推送、订阅管理、推送频率控制等功能
"""

import logging
import time
import json
import threading
from typing import Dict, Set, Optional, Any
from concurrent.futures import Thread<PERSON>oolExecutor
from flask import Flask, request
from flask_socketio import SocketIO, emit, join_room, leave_room
from datetime import datetime

from src.utils.constants import NUMBER_FLG, HISTORICAL_DATA_END, PREV_DAY_CLOSE, WEB_STOCK_LIST
# 全局变量 - 使用新的线程安全数据管理器适配器
from src.data.data_manager_adapter import manager_res_dict_test_adapter as manager_res_dict_test
from collections import deque


class WebSocketServer:
    """WebSocket服务器类"""
    def __init__(self, app: Flask, data_manager):
        # import time
        # print(f"=== [WebSocketServer] 开始初始化WebSocket服务器，时间: {time.time()} ===", flush=True)
        """
        初始化WebSocket服务器
        
        Args:
            app: Flask应用实例
            data_manager: 数据管理器实例
        """
        self.app = app
        self.data_manager = data_manager
        
        # 创建SocketIO实例 - 使用threading模式避免与IB API冲突
        # print(f"=== [WebSocketServer] 创建SocketIO实例，时间: {time.time()} ===", flush=True)
        self.socketio = SocketIO(
            app,
            cors_allowed_origins="*",
            async_mode='threading',  # 改为threading模式，避免eventlet冲突
            logging=False,
            engineio_logger=False
        )
        
        # 订阅管理
        self.subscribers: Dict[str, Set[str]] = {}  # symbol -> {session_ids}
        self.session_subscriptions: Dict[str, Set[str]] = {}  # session_id -> {symbols}
        
        # 推送控制
        self.last_push_time: Dict[str, float] = {}  # symbol -> timestamp
        self.push_interval = 1.0  # 推送间隔（秒）
        
        # 初始数据推送任务管理
        self._initial_push_tasks_lock = threading.Lock()
        self._active_initial_pushes: Set[str] = set()

        # 使用线程池来管理后台任务，防止线程耗尽
        self.task_executor = ThreadPoolExecutor(max_workers=20, thread_name_prefix='WS_Task_')
        
        # 后台推送线程
        self._push_thread: Optional[threading.Thread] = None
        self._stop_event = threading.Event()

        # 新增：用于跟踪股票列表变化
        self.last_known_stock_list: Optional[Set[str]] = None
        self.last_list_check_time: float = 0.0
        self.list_check_interval: float = 2.0  # 每2秒检查一次列表变化

        # 注册事件处理器
        self._register_handlers()
        
        # 启动后台推送线程
        self.start_push_thread()

        # print(f"=== [WebSocketServer] WebSocket服务器初始化完成，时间: {time.time()} ===", flush=True)
        logging.info("WebSocket服务器初始化完成")
    
    def _build_candles_from_dates(self, stock_data_dict, date_list):
        """根据日期列表构建蜡烛图数据 - 时间解析已移到前端处理"""
        candles = []
        for date_str in date_list:
            bar_data = stock_data_dict[date_str]
            try:
                candle = {
                    "time": bar_data["date_time"],  # 直接传递原始时间字符串
                    "open": float(bar_data["Open"]),
                    "high": float(bar_data["High"]),
                    "low": float(bar_data["Low"]),
                    "close": float(bar_data["Close"]),
                    "volume": round(float(bar_data.get("Volume", 0)), 0)
                }
                candles.append(candle)
            except (KeyError, ValueError, TypeError):
                continue
        return candles
    
    def _get_symbol_push_data(self, symbol: str, last_n: int = None):
        """获取格式化后的单只股票推送数据（K线快照+交易次数等）
        last_n: 只取最近N条1分钟数据（如批量推送时用）
        """
        stock_data_dict = self.data_manager.get_stock_data(symbol)
        if not stock_data_dict or not isinstance(stock_data_dict, dict):
            return None
        
        sorted_dates = sorted(stock_data_dict.keys())
        # 过滤掉没有成交量的1分钟数据
        valid_1min_dates = [d for d in sorted_dates if ("Volume" in stock_data_dict[d] and float(stock_data_dict[d]["Volume"]) > 0)]
        
        if last_n is not None:
            # 只取最近N条，数据补齐已移到前端处理
            valid_1min_dates = valid_1min_dates[-last_n:]
            candles = self._build_candles_from_dates(stock_data_dict, valid_1min_dates)
        else:
            # 为确保前端能形成120个5分钟组，提供足够的原始数据
            # 120组 * 5分钟 = 600分钟的数据，考虑缺口可能需要更多
            max_data_points = 800  # 提供800个1分钟数据点
            if len(valid_1min_dates) > max_data_points:
                valid_1min_dates = valid_1min_dates[-max_data_points:]
            candles = self._build_candles_from_dates(stock_data_dict, valid_1min_dates)
        now_ny = datetime.now()
        ny_timestamp = int(now_ny.timestamp())

        # 计算涨幅比
        prev_day_close = self.data_manager.get(symbol, PREV_DAY_CLOSE, 0)
        last_close = None
        if candles:
            last_close = candles[-1]["close"]
        change_pct = None
        if prev_day_close and last_close is not None and prev_day_close > 0:
            change_pct = round((last_close - prev_day_close) / prev_day_close * 100, 2)

        push_data = {
            'symbol': symbol,
            'data': candles,
            'timestamp': ny_timestamp,
            'source': 'websocket_push',
            'transactions': self.data_manager.get(symbol, NUMBER_FLG, 0), # 交易次数
            'change_pct': change_pct # 当天涨幅百分比
        }
        if not candles:
            return None
        return push_data
    
    def _register_handlers(self):
        """注册WebSocket事件处理器"""
        # ...existing code...
        @self.socketio.on('connect')
        def handle_connect(auth=None):
            try:
                session_id = self._get_session_id()
                self.session_subscriptions[session_id] = set()
                emit('status', {
                    'type': 'connected',
                    'message': 'WebSocket连接成功',
                    'timestamp': time.time()
                })
            except Exception as e:
                logging.error(f"WebSocket连接处理失败: {e}")
                
        @self.socketio.on('disconnect')
        def handle_disconnect(data=None):
            try:
                session_id = self._get_session_id()
                if session_id in self.session_subscriptions:
                    symbols = self.session_subscriptions[session_id].copy()
                    for symbol in symbols:
                        self._unsubscribe_symbol(session_id, symbol)
                    del self.session_subscriptions[session_id]
            except Exception as e:
                logging.error(f"WebSocket断开连接处理失败: {e}")
                
        @self.socketio.on('subscribe')
        def handle_subscribe(data):
            try:
                session_id = self._get_session_id()
                symbol = data.get('symbol')
                if not symbol:
                    emit('error', {'message': '股票代码不能为空'})
                    return
                is_new_subscription = self._subscribe_symbol(session_id, symbol)
                emit('subscription_status', {
                    'symbol': symbol,
                    'status': 'subscribed',
                    'timestamp': time.time()
                })
                if is_new_subscription:
                    self.task_executor.submit(self._initial_push_task, symbol)
            except Exception as e:
                logging.error(f"WebSocket订阅处理失败: {e}")
                emit('error', {'message': f'订阅失败: {str(e)}'})
                
        @self.socketio.on('unsubscribe')
        def handle_unsubscribe(data):
            try:
                session_id = self._get_session_id()
                symbol = data.get('symbol')
                if not symbol:
                    emit('error', {'message': '股票代码不能为空'})
                    return
                self._unsubscribe_symbol(session_id, symbol)
                emit('subscription_status', {
                    'symbol': symbol,
                    'status': 'unsubscribed',
                    'timestamp': time.time()
                })
            except Exception as e:
                logging.error(f"WebSocket取消订阅处理失败: {e}")
                emit('error', {'message': f'取消订阅失败: {str(e)}'})

        @self.socketio.on('get_all_data')
        def handle_get_all_data(data=None):
            try:
                data = self.data_manager.get_all_stock_data()
                emit('all_data', {
                    'data': data,
                    'timestamp': time.time(),
                    'source': 'websocket'
                })
            except Exception as e:
                logging.error(f"获取所有数据失败: {e}")
                emit('error', {'message': '获取数据失败'})

        @self.socketio.on('get_symbol_data')
        def handle_get_symbol_data(data):
            symbol = data.get('symbol')
            if not symbol:
                emit('error', {'message': '股票代码不能为空'})
                return
            try:
                symbol_data = self.data_manager.get_stock_data(symbol)
                emit('symbol_data', {
                    'symbol': symbol,
                    'data': symbol_data,
                    'timestamp': time.time(),
                    'source': 'websocket'
                })
            except Exception as e:
                logging.error(f"获取{symbol}数据失败: {e}")
                emit('error', {'message': f'获取{symbol}数据失败'})

        @self.socketio.on('request_stocks_list')
        def handle_request_stocks_list(data=None):
            try:
                session_id = self._get_session_id()
                self.task_executor.submit(self._wait_and_push_list_task, session_id)
            except Exception as e:
                logging.error(f"WebSocket请求股票列表处理失败: {e}")
                emit('error', {'message': f'获取股票列表失败: {str(e)}'})
    
    def _get_session_id(self) -> str:
        """获取当前会话ID"""
        return request.sid
    
    def _subscribe_symbol(self, session_id: str, symbol: str) -> bool:
        """
        订阅股票符号，此方法是幂等的。
        如果是一个新的订阅，则返回 True，否则返回 False。
        """
        # 检查此会话是否已订阅该股票
        if session_id in self.session_subscriptions and symbol in self.session_subscriptions[session_id]:
            return False

        # 添加到全局订阅列表
        if symbol not in self.subscribers:
            self.subscribers[symbol] = set()
        self.subscribers[symbol].add(session_id)
        
        # 添加到会话订阅列表
        if session_id not in self.session_subscriptions:
            self.session_subscriptions[session_id] = set()
        self.session_subscriptions[session_id].add(symbol)
        
        # 加入SocketIO房间以便进行广播
        join_room(f"symbol_{symbol}")
        
        return True
    
    def _unsubscribe_symbol(self, session_id: str, symbol: str):
        """取消订阅股票符号"""
        # 从订阅列表移除
        if symbol in self.subscribers:
            self.subscribers[symbol].discard(session_id)
            if not self.subscribers[symbol]:
                del self.subscribers[symbol]
        
        # 从会话订阅列表移除
        if session_id in self.session_subscriptions:
            self.session_subscriptions[session_id].discard(symbol)
        
        # 离开房间
        leave_room(f"symbol_{symbol}")
    
    def _push_symbol_data(self, symbol: str, force: bool = False):
        """推送指定股票数据，格式化为客户端期望的数组格式"""
        # 检查推送频率限制
        if not force:
            current_time = time.time()
            if symbol in self.last_push_time:
                if current_time - self.last_push_time[symbol] < self.push_interval:
                    return
            self.last_push_time[symbol] = current_time
        # 检查是否有订阅者
        if symbol not in self.subscribers or not self.subscribers[symbol]:
            logging.warning(f"{symbol} 没有订阅者，跳过推送。")
            return
        try:
            push_data = self._get_symbol_push_data(symbol)
            if not push_data:
                logging.warning(f"{symbol} 的格式化K线数据为空，跳过推送。")
                return
            self.socketio.emit('data', push_data, room=f"symbol_{symbol}")
        except Exception as e:
            logging.error(f"推送{symbol}数据失败: {e}", exc_info=True)

    def _initial_push_task(self, symbol: str):
        """
        在后台处理初始数据推送的函数。
        它会等待数据准备就绪（HISTORICAL_DATA_END == 1），然后执行一次强制推送。
        这可以防止重复的订阅请求导致不必要的数据拉取。
        """
        with self._initial_push_tasks_lock:
            if symbol in self._active_initial_pushes:
                return
            self._active_initial_pushes.add(symbol)

        try:
            start_time = time.time()
            timeout = 59  # 增加超时以防数据加载缓慢
            while time.time() - start_time < timeout:
                # 1. 首先检查历史数据是否已完全加载
                historical_data_ended = self.data_manager.get(symbol, HISTORICAL_DATA_END, 0) == 1
                if historical_data_ended:
                    break
                time.sleep(0.5)  # 短暂等待后重试

            if historical_data_ended:
                self._push_symbol_data(symbol, force=True)
            else:
                logging.error(f"等待 {symbol} 的数据超时或数据不完整（{timeout}秒），无法完成初始推送。")

        except Exception as e:
            logging.error(f"{symbol} 的初始推送任务失败: {e}")
        finally:
            # 任务完成后，从活动任务集合中移除
            with self._initial_push_tasks_lock:
                self._active_initial_pushes.discard(symbol)
    
    def _wait_and_push_list_task(self, session_id: str):
        """
        在后台等待股票列表准备就绪，然后将其推送给特定的客户端。
        这可以防止在Web服务器启动初期，由于数据获取线程尚未完成而返回空列表的竞态条件。
        """
        try:
            start_time = time.time()
            timeout = 15  # 等待15秒

            while time.time() - start_time < timeout:
                stock_codes = manager_res_dict_test.get(WEB_STOCK_LIST, [])
                
                if stock_codes:
                    self._push_stocks_list(session_id)
                    return  # 任务完成

                # logging.warning(f"股票列表为空，为会话 {session_id} 等待0.5秒后重试...")
                time.sleep(0.5)

            # 如果超时
            # logging.warning(f"等待股票列表超时 ({timeout}秒)，为会话 {session_id} 推送当前列表（可能为空）。")
            self._push_stocks_list(session_id)

        except Exception as e:
            logging.error(f"执行 {session_id} 的等待并推送列表任务时出错: {e}", exc_info=True)

    def _check_and_broadcast_stock_list_changes(self):
        """检查股票列表是否发生变化，如果变化则广播给所有客户端"""
        try:
            # 获取当前股票列表并转换为集合以便比较
            current_stock_codes = set(manager_res_dict_test.get(WEB_STOCK_LIST, []))
            
            # 如果是第一次检查，则只记录当前列表状态
            if self.last_known_stock_list is None:
                self.last_known_stock_list = current_stock_codes
                return

            # 如果列表发生变化
            if current_stock_codes != self.last_known_stock_list:
                
                # 更新记录的列表
                self.last_known_stock_list = current_stock_codes
                
                # 广播新的股票列表给所有客户端
                self._push_stocks_list() # 调用时不带 session_id 即为广播
                
        except Exception as e:
            logging.error(f"检查股票列表变化时出错: {e}", exc_info=True)

    def _push_stocks_list(self, session_id: str = None):
        """推送股票列表"""
        try:
            # 获取股票列表
            stock_list = manager_res_dict_test.get(WEB_STOCK_LIST, [])

            if not stock_list:
                return  # 如果列表为空，则不进行推送
            
            if session_id:
                # 发送给特定客户端
                self.socketio.emit('stocks_list', stock_list, to=session_id)
            else:
                # 广播给所有客户端
                self.socketio.emit('stocks_list', stock_list)

            # logging.info(f"推送股票列表成功: {len(stock_codes)} 个股票")
            
        except Exception as e:
            logging.error(f"推送股票列表失败: {e}")
            if session_id:
                self.socketio.emit('stocks_list', [], to=session_id)
            else:
                self.socketio.emit('stocks_list', [])

    def push_all_data(self):
        """批量推送所有已订阅且历史数据已完成的股票数据（all_data事件）"""
        subscribed_symbols = list(self.subscribers.keys())
        all_data_list = []
        for symbol in subscribed_symbols:
            # 判断HISTORICAL_DATA_END==1
            historical_data_ended = self.data_manager.get(symbol, HISTORICAL_DATA_END, 0) == 1
            if not historical_data_ended:
                continue
            # 只取最后20条1分钟数据
            push_data = self._get_symbol_push_data(symbol, last_n=20)
            if push_data:
                all_data_list.append(push_data)
        self.socketio.emit('all_data', all_data_list)
    
    def broadcast_update(self, data: Dict[str, Any]):
        """广播数据更新"""
        self.socketio.emit('broadcast_update', {
            'data': data,
            'timestamp': time.time(),
            'source': 'broadcast'
        })
    
    def get_subscriber_count(self, symbol: str = None) -> int:
        """获取订阅者数量"""
        if symbol:
            return len(self.subscribers.get(symbol, set()))
        else:
            return sum(len(subs) for subs in self.subscribers.values())
    
    def get_active_symbols(self) -> list:
        """获取有订阅者的股票列表"""
        return list(self.subscribers.keys())

    def start_push_thread(self):
        """启动后台推送线程"""
        if self._push_thread is None or not self._push_thread.is_alive():
            self._stop_event.clear()
            self._push_thread = threading.Thread(target=self._push_loop, daemon=True, name="WebSocketPushThread")
            self._push_thread.start()
            logging.info("WebSocket后台推送线程已启动")

    def _push_loop(self):
        """后台推送循环"""
        logging.info("推送循环开始")
        while not self._stop_event.is_set():
            try:
                # 检查股票列表是否有变化并广播
                current_time = time.time()
                if current_time - self.last_list_check_time > self.list_check_interval:
                    self._check_and_broadcast_stock_list_changes()
                    self.last_list_check_time = current_time

                # 推送已订阅股票的实时数据
                self.push_all_data()
            except Exception as e:
                logging.error(f"推送循环中发生错误: {e}")
            # 控制循环频率，例如每秒推送10次
            time.sleep(0.5)
        logging.info("推送循环已停止")

    def stop_push_thread(self):
        """停止后台推送线程"""
        if self._push_thread and self._push_thread.is_alive():
            self._stop_event.set()
            self._push_thread.join(timeout=2)
            logging.info("WebSocket后台推送线程已停止")


def init_websocket_server(app: Flask, data_manager) -> WebSocketServer:
    """
    初始化WebSocket服务器
    
    Args:
        app: Flask应用实例
        data_manager: 数据管理器实例
    
    Returns:
        WebSocketServer实例
    """
    return WebSocketServer(app, data_manager)


# 全局实例引用（可选）
_websocket_server_instance: Optional[WebSocketServer] = None


def get_websocket_server() -> Optional[WebSocketServer]:
    """获取WebSocket服务器实例"""
    return _websocket_server_instance


def set_websocket_server(server: WebSocketServer):
    """设置WebSocket服务器实例"""
    global _websocket_server_instance
    _websocket_server_instance = server


# 示例使用方法
if __name__ == "__main__":
    app = Flask(__name__)
    
    # 模拟数据管理器
    class MockDataManager:
        def get_all_stock_data(self):
            return {"AAPL": {"price": 150.0}, "GOOGL": {"price": 2500.0}}