/**
 * 图表管理器 NPM ES模块版本
 * 使用npm安装的TradingView Lightweight Charts，这是官方推荐的标准方式
 */

// 从npm包导入（官方推荐方式）
import { createChart, CandlestickSeries, LineSeries, HistogramSeries, CrosshairMode, createSeriesMarkers } from 'lightweight-charts';

export class ChartManager {
    constructor() {
        this.charts = new Map(); // 存储所有图表实例
        this.chartData = new Map(); // 存储图表数据
        this.updateQueues = new Map(); // 更新队列，避免过频繁更新
        this.isUpdating = false;
        
        // 增量聚合相关数据结构
        this.oneMinData = new Map(); // symbol => 1分钟K线原始数据
        this.fiveMinData = new Map(); // symbol => 5分钟K线聚合数据
        this.lastUpdateHash = new Map(); // symbol => 数据哈希值，用于快速变化检测
        // this.performanceMonitor = new PerformanceMonitor();
        this.dataManager = new DataManager();
        
        // 增量更新配置
        this.incrementalUpdateEnabled = true; // 默认启用增量更新
        
        // 回放模式数据结构
        this.playbackData = null; // 存储预处理的回放数据
        this.playbackSettings = {
            startTime: null,      // 回放开始时间（时间戳）
            speed: 1000,          // 回放速度（毫秒）
            isPlaying: false      // 是否正在回放
        };
        
        // 图表配置
        this.chartOptions = {
            layout: {
                background: { color: "#1f2937" },
                textColor: "#e5e7eb"
            },
            grid: {
                vertLines: { color: "#374151" },
                horzLines: { color: "#374151" }
            },
            timeScale: {
                timeVisible: true,
                secondsVisible: false,
                borderColor: "#6b7280",
                rightOffset: 2,
                // 控制K线宽度，10为常用视觉宽度，可根据需求调整
                barSpacing: 10,
                // 与 index (2).html 保持一致，定制时间轴格式
                tickMarkFormatter: (time, tickMarkType, locale) => {
                    const date = new Date(time * 1000);
                    const nyTimeStr = date.toLocaleString('en-US', {
                        timeZone: 'America/New_York',
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        hour12: false
                    });
                    if (tickMarkType === 0) {
                        return nyTimeStr.split(',')[0].split('/')[2];
                    } else if (tickMarkType === 1) {
                        const parts = nyTimeStr.split(',')[0].split('/');
                        return `${parts[2]}/${parts[0]}`;
                    } else if (tickMarkType === 2) {
                        const parts = nyTimeStr.split(',')[0].split('/');
                        return `${parts[0]}/${parts[1]}`;
                    } else {
                        const timePart = nyTimeStr.split(', ')[1];
                        return timePart;
                    }
                }
            },
            rightPriceScale: {
                borderColor: "#6b7280"
            },
            localization: {
                timeFormatter: (time) => {
                    const date = new Date(time * 1000);
                    const nyTimeStr = date.toLocaleString('en-US', {
                        timeZone: 'America/New_York',
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        hour12: false
                    });
                    const formatted = nyTimeStr.replace(/(\d+)\/(\d+)\/(\d+),?\s*(\d+):(\d+)/, '$3/$1/$2 $4:$5');
                    return `${formatted} ET`;
                },
                priceFormatter: (price) => {
                    // 如果价格小于1，显示4位小数
                    if (Math.abs(price) < 1) {
                        return price.toFixed(4);
                    }
                    // 其他情况显示2位小数
                    return price.toFixed(2);
                }
            },
            crosshair: {
                mode: 0, // CrosshairMode.Normal
            },
            width: 0,  // 将由resize设置
            height: 0  // 将由resize设置
        };
        
        this.seriesOptions = {
            upColor: '#10b981',
            downColor: '#ef4444',
            borderVisible: false,
            wickUpColor: '#10b981',
            wickDownColor: '#ef4444',
            priceFormat: {
                type: 'price',
                precision: 4,  // 设置4位小数
                minMove: 0.0001,  // 最小价格变动单位
            }
        };
    }
    
    /**
     * 创建新图表
     * @param {string} containerId 容器ID
     * @param {string} symbol 股票代码
     * @returns {Object} 图表实例
     */
    createChart(containerId, symbol) {
        
        try {
            // 检查容器是否存在
            const container = document.getElementById(containerId);
            if (!container) {
                console.error(`${symbol} 容器不存在: ${containerId}`);
                return null;
            }
            
            // 创建图表
            const chart = createChart(container, {
                ...this.chartOptions,
                width: container.offsetWidth,
                height: container.offsetHeight || 350,
            });

            // 创建K线系列
            const candlestickSeries = chart.addSeries(CandlestickSeries, this.seriesOptions);

            // 创建EMA系列
            const ema9Series = chart.addSeries(LineSeries, { 
                color: '#ff9800', 
                lineWidth: 1 
            });
            
            const ema20Series = chart.addSeries(LineSeries, { 
                color: '#f321e5', 
                lineWidth: 2 
            });
            
            const ema120Series = chart.addSeries(LineSeries, { 
                color: '#41f321', 
                lineWidth: 3 
            });

            // 设置主价格轴边距
            chart.priceScale('right').applyOptions({
                scaleMargins: { top: 0.1, bottom: 0.25 },
            });

            // 创建MACD系列（添加到窗格1）
            const macdLineSeries = chart.addSeries(LineSeries, { 
                color: '#2196F3', 
                lineWidth: 2 
            }, 1); // 添加到窗格1
            const signalLineSeries = chart.addSeries(LineSeries, { 
                color: '#FF9800', 
                lineWidth: 1 
            }, 1); // 添加到窗格1
            const macdHistSeries = chart.addSeries(HistogramSeries, { 
                color: '#26a69a' 
            }, 1); // 添加到窗格1

            // 立即设置图表尺寸
            setTimeout(() => {
                try {
                    const rect = container.getBoundingClientRect();
                    if (rect.width > 0 && rect.height > 0) {
                        chart.applyOptions({ width: rect.width, height: rect.height });
                    } else {
                        // 如果容器尺寸为0，使用默认尺寸
                        chart.applyOptions({ width: container.clientWidth || 400, height: 300 });
                    }
                } catch (e) {
                    console.error(`[调试] ${symbol} 设置图表尺寸失败:`, e);
                }
            }, 100);

            // 创建突破标记插件
            const seriesMarkersPlugin = createSeriesMarkers(candlestickSeries, []);
            const surgeMarkersData = {
                plugin: seriesMarkersPlugin, // 保存标记插件用于后续操作
                series: candlestickSeries,   // 保存系列引用
                lines: []
            };

            // 窗口大小变化时调整图表大小
            const resizeObserver = new ResizeObserver(entries => {
                try {
                    for (let entry of entries) {
                        const { width, height } = entry.contentRect;
                        chart.applyOptions({ width, height });
                    }
                } catch (e) {
                    console.error(`[调试] ${symbol} ResizeObserver错误:`, e);
                }
            });
            resizeObserver.observe(container);

            // 存储图表信息
            const chartInfo = {
                chart: chart,
                series: candlestickSeries,
                container: container,
                containerId: containerId,
                // 技术指标系列
                ema9Series: ema9Series,
                ema20Series: ema20Series,
                ema120Series: ema120Series,
                macdLineSeries: macdLineSeries,
                signalLineSeries: signalLineSeries,
                macdHistSeries: macdHistSeries,
                // 价格突破检测系列
                surgeMarkers: surgeMarkersData,
                resizeObserver: resizeObserver // 保存observer用于后续销毁
            };

            // 添加到图表集合
            this.charts.set(symbol, chartInfo);
            this.chartData.set(symbol, []);
            this.updateQueues.set(symbol, []);

            // 初始化容器的荧光边框状态为默认（无荧光）
            const chartContainer = document.getElementById(`chart-container-${symbol}`);
            if (chartContainer) {
                chartContainer.classList.add('glow-none');
            }
            
            return chartInfo;
        } catch (error) {
            console.error(`${symbol} 创建图表失败:`, error);
            return null;
        }
    }
    
    /**
     * 获取指定股票的图表
     * @param {string} symbol - 股票代码
     * @returns {Object|null} 图表对象或null
     */
    getChart(symbol) {
        return this.charts.get(symbol) || null;
    }
    
    /**
     * 更新图表数据（支持增量更新）
     * @param {string} symbol 股票代码
     * @param {Array} newData 新的K线数据
     * @param {boolean} useIncremental 是否使用增量更新
     */
    updateChart(symbol, newData, useIncremental = true) {
        if (!this.charts.has(symbol)) {
            console.warn(`图表不存在: ${symbol}`);
            return;
        }
        
        // 检查是否应该使用增量更新
        if (useIncremental && this.incrementalUpdateEnabled && newData && newData.length > 0) {
            try {
                this.updateChartIncremental(symbol, newData);
                return; // 增量更新成功，直接返回
            } catch (error) {
                console.warn(`增量更新失败，回退到全量更新: ${symbol}`, error);
                // 继续执行全量更新
            }
        }
        
        // 添加到更新队列（全量更新）
        this.updateQueues.set(symbol, newData);
        
        // 节流更新
        //"更新锁"，用于防止图表批量更新的并发和过度频繁触发，确保每一帧只处理一次队列里的所有更新
        if (!this.isUpdating) {
            this.isUpdating = true;
            requestAnimationFrame(() => {
                this.processUpdateQueues();
                this.isUpdating = false;
            });
        }
    }
    
    /**
     * 处理更新队列
     */
    processUpdateQueues() {
        this.updateQueues.forEach((newData, symbol) => {
            this.doUpdateChart(symbol, newData);
        });
        this.updateQueues.clear();
    }
    
    /**
     * 实际执行图表更新
     * @param {string} symbol 股票代码
     * @param {Array} newData 新的K线数据
     */
    doUpdateChart(symbol, newData) {
        const chartInfo = this.charts.get(symbol);
        if (!chartInfo) return;
        try {
            // 转换数据格式
            const formattedData = this.formatChartData(newData);
            
            // 将1分钟数据聚合为5分钟数据
            let aggregatedData = formattedData;
            aggregatedData = this.aggregateTo5MinData(formattedData);
            
            // 检查数据是否有变化
            const currentData = this.chartData.get(symbol);
            if (this.isDataEqual(currentData, aggregatedData)) {
                // console.log(`[调试] ${symbol} 数据无变化，跳过更新`);
                return; // 数据无变化，跳过更新
            }

            // open=close时用阴线色
            const downColor = this.seriesOptions.downColor;
            const coloredData = aggregatedData.map(item => {
                if (item.open === item.close) {
                    return { ...item, color: downColor }; 
                }
                return item;
            });
            // 更新K线图表
            chartInfo.series.setData(coloredData);
            // console.log(`全量更新K线图表 (doUpdateChart): ${symbol}`, coloredData);

            // 更新本地数据缓存
            this.chartData.set(symbol, aggregatedData);
            
            // 计算并更新技术指标
            if (aggregatedData.length > 0) {
                // 计算EMA指标
                const ema9 = this.calculateEMA(aggregatedData, 9);
                const ema20 = this.calculateEMA(aggregatedData, 20);
                const ema120 = this.calculateEMA(aggregatedData, 120);
                
                // 更新EMA系列
                try {
                    if (chartInfo.ema9Series) chartInfo.ema9Series.setData(ema9);
                    if (chartInfo.ema20Series) chartInfo.ema20Series.setData(ema20);
                    if (chartInfo.ema120Series) chartInfo.ema120Series.setData(ema120);
                } catch (error) {
                    console.error(`[调试] ${symbol} 设置EMA指标失败:`, error);
                }

                // 计算MACD指标
                const macdResult = this.calculateMACD(aggregatedData);
                
                // 更新MACD系列
                try {
                    if (chartInfo.macdLineSeries) chartInfo.macdLineSeries.setData(macdResult.macdLine);
                    if (chartInfo.signalLineSeries) chartInfo.signalLineSeries.setData(macdResult.signalLine);
                    if (chartInfo.macdHistSeries) chartInfo.macdHistSeries.setData(macdResult.hist);
                } catch (error) {
                    console.error(`[调试] ${symbol} 设置MACD指标失败:`, error);
                }
            }
            
            // 检测并渲染突破信号
            const surgeData = this.calculatePriceSurgeDetector(aggregatedData);
            
            // 检查是否有信号被移除（价格失效）
            const currentSignals = this.chartData.get(symbol + '_signals') || [];
            if (currentSignals.length > surgeData.signals.length) {
            }
            this.chartData.set(symbol + '_signals', surgeData.signals);
            
            if (chartInfo && chartInfo.surgeMarkers && chartInfo.surgeMarkers.plugin) {
                try {
                    // 使用createSeriesMarkers插件API设置标记
                    chartInfo.surgeMarkers.plugin.setMarkers(surgeData.signals);
                } catch (error) {
                    console.error(`[调试] ${symbol} 标记插件设置失败:`, error);
                }
            }

            // 更新形态文字（增加跌破检查功能）
            let shapeText = '';
            
            if (surgeData.signals.length > 0) {
                const latestSignal = surgeData.signals[surgeData.signals.length - 1];
                if (latestSignal.text.includes('突破')) {
                    shapeText = '突破';
                } else if (latestSignal.text.includes('形态')) {
                    shapeText = '形态';
                }
            }
            
            // 检查形态完成后是否跌破最后一根突破K线（无论当前是否显示形态文字）
            if (aggregatedData.length > 0) {
                const currentCandle = aggregatedData[aggregatedData.length - 1];
                const shouldClearText = this.checkBreakdownAfterCompletion(surgeData.signals, currentCandle);
                
                if (shouldClearText) {
                    shapeText = ''; // 清除形态文字
                }
            }
            
            const surgeEl = document.getElementById(`surge-${symbol}`);
            const containerEl = document.getElementById(`chart-container-${symbol}`);
            
            if (surgeEl) {
                const previousText = surgeEl.textContent;
                surgeEl.textContent = shapeText;
                
                // 设置样式类 - 突破为绿色，形态为蓝色
                if (shapeText.includes('突破')) {
                    surgeEl.className = 'ml-2 font-medium text-green-400';
                    // 为容器添加绿色荧光边框效果
                    if (containerEl) {
                        containerEl.classList.remove('glow-blue', 'glow-none');
                        containerEl.classList.add('glow-green');
                    } else {
                    }
                } else if (shapeText.includes('形态')) {
                    surgeEl.className = 'ml-2 font-medium text-blue-400';
                    // 为容器添加蓝色荧光边框效果
                    if (containerEl) {
                        containerEl.classList.remove('glow-green', 'glow-none');
                        containerEl.classList.add('glow-blue');
                    }
                } else {
                    surgeEl.className = 'ml-2 font-medium';
                    // 移除荧光边框效果，恢复默认状态
                    if (containerEl) {
                        containerEl.classList.remove('glow-green', 'glow-blue');
                        containerEl.classList.add('glow-none');
                    }
                }
            } else {
                console.warn(`[调试] ${symbol} 突破文字元素不存在`);
            }

            // 自动保持 rightOffset=2，显示全部K线
            if (aggregatedData.length > 0) {
                try {
                    chartInfo.chart.applyOptions({ timeScale: { rightOffset: 2 } });
                } catch (error) {
                    console.error(`[调试] ${symbol} 设置rightOffset失败:`, error);
                }
            }
        } catch (error) {
            console.error(`${symbol} 更新图表失败:`, error);
        }
    }
    
    /**
     * 更新当天涨幅百分比（如有change_pct字段）
     * @param {string} symbol 股票代码
     * @param {Object} change_pct 涨幅百分比
     */
    updateChangePercentage(symbol, change_pct) {
        // 更新当天涨幅百分比（如有change_pct字段）
        if (change_pct !== undefined) {
            const changeSpan = document.getElementById(`change-${symbol}`);
            if (changeSpan) {
                let sign = change_pct > 0 ? '+' : (change_pct < 0 ? '' : '');
                changeSpan.textContent = `${sign}${change_pct.toFixed(2)}%`;
                changeSpan.className = change_pct > 0 ? 'text-green-400 font-bold' : (change_pct < 0 ? 'text-red-400 font-bold' : 'price-neutral');
            }
        }
    }
    
    /**
     * 格式化图表数据
     * @param {Array} rawData 原始数据
     * @returns {Array} 格式化后的数据
     */
    formatChartData(rawData) {
        if (!Array.isArray(rawData)) {
            return [];
        }
        
        return rawData
            .filter(item => item && typeof item === 'object')
            .map(item => ({
                time: item.time,
                open: parseFloat(item.open) || 0,
                high: parseFloat(item.high) || 0,
                low: parseFloat(item.low) || 0,
                close: parseFloat(item.close) || 0,
                volume: parseFloat(item.volume) || 0 // 添加成交量字段，聚合函数需要
            }))
            .filter(item => item.time > 0) // 过滤无效时间
            .sort((a, b) => a.time - b.time); // 按时间排序
    }
    
    /**
     * 检查数据是否相等
     * @param {Array} data1 数据1
     * @param {Array} data2 数据2
     * @returns {boolean} 是否相等
     */
    isDataEqual(data1, data2) {
        if (!data1 || !data2) return false;
        if (data1.length !== data2.length) return false;
        
        // 只比较最后几个数据点，提高性能
        const compareCount = Math.min(5, data1.length);
        for (let i = 1; i <= compareCount; i++) {
            const idx = data1.length - i;
            if (idx < 0) break;
            
            const item1 = data1[idx];
            const item2 = data2[idx];
            
            if (item1.time !== item2.time ||
                item1.open !== item2.open ||
                item1.high !== item2.high ||
                item1.low !== item2.low ||
                item1.close !== item2.close ||
                item1.volume !== item2.volume) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 添加实时数据点
     * @param {string} symbol 股票代码
     * @param {Object} dataPoint 新数据点
     */
    addDataPoint(symbol, dataPoint) {
        if (!this.charts.has(symbol)) {
            return;
        }
        
        const chartInfo = this.charts.get(symbol);
        const formattedPoint = {
            time: dataPoint.time,
            open: parseFloat(dataPoint.open) || 0,
            high: parseFloat(dataPoint.high) || 0,
            low: parseFloat(dataPoint.low) || 0,
            close: parseFloat(dataPoint.close) || 0
        };
        
        try {
            if (formattedPoint.open === formattedPoint.close) {
                formattedPoint.color = this.seriesOptions.downColor;
            }
            chartInfo.series.update(formattedPoint);
            
            // 更新本地数据缓存
            const currentData = this.chartData.get(symbol) || [];
            const existingIndex = currentData.findIndex(item => item.time === formattedPoint.time);
            
            if (existingIndex >= 0) {
                currentData[existingIndex] = formattedPoint;
            } else {
                currentData.push(formattedPoint);
                currentData.sort((a, b) => a.time - b.time);
            }
            
            this.chartData.set(symbol, currentData);

            // 重新计算并更新技术指标
            if (currentData.length > 0) {
                // 计算EMA指标
                const ema9 = this.calculateEMA(currentData, 9);
                const ema20 = this.calculateEMA(currentData, 20);
                const ema120 = this.calculateEMA(currentData, 120);
                
                // 更新EMA系列
                if (chartInfo.ema9Series) chartInfo.ema9Series.setData(ema9);
                if (chartInfo.ema20Series) chartInfo.ema20Series.setData(ema20);
                if (chartInfo.ema120Series) chartInfo.ema120Series.setData(ema120);

                // 计算MACD指标
                const macdResult = this.calculateMACD(currentData);
                
                // 更新MACD系列
                if (chartInfo.macdLineSeries) chartInfo.macdLineSeries.setData(macdResult.macdLine);
                if (chartInfo.signalLineSeries) chartInfo.signalLineSeries.setData(macdResult.signalLine);
                if (chartInfo.macdHistSeries) chartInfo.macdHistSeries.setData(macdResult.hist);

                // 更新突破检测标记
                const surgeData = this.calculatePriceSurgeDetector(currentData);
                if (chartInfo.surgeMarkers && chartInfo.surgeMarkers.plugin) {
                    try {
                        chartInfo.surgeMarkers.plugin.setMarkers(surgeData.signals);
                    } catch (error) {
                        console.error(`[addDataPoint] ${symbol} 标记插件设置失败:`, error);
                    }
                }
                
                // 更新形态文字显示
                this.updateSurgeTextDisplay(symbol, surgeData.signals, currentData);
            }
            
        } catch (error) {
            console.error(`添加数据点失败 ${symbol}:`, error);
        }
    }
    
        // 删除过时的技术指标方法
        // updateTechnicalIndicators, calculateEMAValues等方法已被新的完整实现替代
    
    /**
     * 计算EMA指标
     * @param {Array} data 价格数据
     * @param {number} period 周期
     * @returns {Array} EMA数据
     */
    calculateEMA(data, period) {
        // 如果volume为0，用上一根k线的open
        const prices = [];
        for (let i = 0; i < data.length; i++) {
            if (data[i].volume === 0 && i > 0) {
                prices.push(prices[i - 1]);
            } else {
                prices.push(data[i].open);
            }
        }
        const times = data.map(d => d.time);
        const result = [];
        if (prices.length === 0) return result;

        const k = 2 / (period + 1);
        let ema = 0;
        // 前 period-1 个点用SMA（简单均线）填充
        for (let i = 0; i < prices.length; i++) {
            if (i < period - 1) {
                // 用SMA
                let sum = 0;
                for (let j = 0; j <= i; j++) {
                    sum += prices[j];
                }
                ema = sum / (i + 1);
                result.push({ time: times[i], value: ema });
            } else if (i === period - 1) {
                // 第period个点，SMA初始化
                let sum = 0;
                for (let j = i - period + 1; j <= i; j++) {
                    sum += prices[j];
                }
                ema = sum / period;
                result.push({ time: times[i], value: ema });
            } else {
                ema = prices[i] * k + ema * (1 - k);
                result.push({ time: times[i], value: ema });
            }
        }
        return result;
    }

    /**
     * 计算MACD指标
     * @param {Array} data 价格数据
     * @param {number} fastPeriod 快速周期  
     * @param {number} slowPeriod 慢速周期
     * @param {number} signalPeriod 信号周期
     * @returns {Object} MACD数据
     */
    calculateMACD(data, fastPeriod = 12, slowPeriod = 26, signalPeriod = 9) {
        if (!data || data.length === 0) return { macdLine: [], signalLine: [], histogram: [] };

        // 计算快速和慢速EMA，前n点用SMA填充
        const prices = [];
        for (let i = 0; i < data.length; i++) {
            if (data[i].volume === 0 && i > 0) {
                prices.push(prices[i - 1]);
            } else {
                prices.push(data[i].close);
            }
        }

        // 计算EMA（前n点用SMA填充）
        function calcEMAWithSMA(prices, period) {
            const emaArr = [];
            const k = 2 / (period + 1);
            let ema = 0;
            for (let i = 0; i < prices.length; i++) {
                if (i < period - 1) {
                    // 用SMA
                    let sum = 0;
                    for (let j = 0; j <= i; j++) {
                        sum += prices[j];
                    }
                    ema = sum / (i + 1);
                    emaArr.push(ema);
                } else if (i === period - 1) {
                    let sum = 0;
                    for (let j = i - period + 1; j <= i; j++) {
                        sum += prices[j];
                    }
                    ema = sum / period;
                    emaArr.push(ema);
                } else {
                    ema = prices[i] * k + ema * (1 - k);
                    emaArr.push(ema);
                }
            }
            return emaArr;
        }

        const fastEMA = calcEMAWithSMA(prices, fastPeriod);
        const slowEMA = calcEMAWithSMA(prices, slowPeriod);
        const macdLine = [];
        const signalLine = [];
        const histogram = [];

        // 计算MACD线
        for (let i = 0; i < data.length; i++) {
            // 只要fastEMA和slowEMA有值即可，已保证无null
            const macdValue = fastEMA[i] - slowEMA[i];
            macdLine.push({
                time: data[i].time,
                value: macdValue
            });
        }

        // 信号线（MACD的EMA，前n点用SMA填充）
        function calcEMAFromValuesWithSMA(values, period) {
            const emaArr = [];
            const k = 2 / (period + 1);
            let ema = 0;
            for (let i = 0; i < values.length; i++) {
                if (i < period - 1) {
                    let sum = 0;
                    for (let j = 0; j <= i; j++) {
                        sum += values[j];
                    }
                    ema = sum / (i + 1);
                    emaArr.push(ema);
                } else if (i === period - 1) {
                    let sum = 0;
                    for (let j = i - period + 1; j <= i; j++) {
                        sum += values[j];
                    }
                    ema = sum / period;
                    emaArr.push(ema);
                } else {
                    ema = values[i] * k + ema * (1 - k);
                    emaArr.push(ema);
                }
            }
            return emaArr;
        }

        const macdValues = macdLine.map(item => item.value);
        const signalEMA = calcEMAFromValuesWithSMA(macdValues, signalPeriod);

        for (let i = 0; i < macdLine.length; i++) {
            // signalEMA已保证无null
            signalLine.push({
                time: macdLine[i].time,
                value: signalEMA[i]
            });
        }

        // 计算直方图（MACD - Signal）
        for (let i = 0; i < Math.min(macdLine.length, signalLine.length); i++) {
            // macdLine和signalLine已保证无null
            const macdVal = macdLine[i].value;
            const signalVal = signalLine[i].value;
            const histValue = macdVal - signalVal;
            histogram.push({
                time: macdLine[i].time,
                value: histValue,
                color: histValue >= 0 ? '#26a69a' : '#ef5350'
            });
        }

        return {
            macdLine,
            signalLine,
            hist: histogram
        };
    }
    
    /**
     * 检测价格突破
     * @param {string} symbol - 股票代码
     * @param {Array} klineData - K线数据
     */
    detectPriceSurge(symbol, klineData) {
        if (klineData.length < 20) return;
        
        const chartInfo = this.charts.get(symbol);
        if (!chartInfo) return;
        
        const recentBars = klineData.slice(-20);
        const currentBar = recentBars[recentBars.length - 1];
        const avgVolume = recentBars.slice(0, -1).reduce((sum, bar) => sum + bar.volume, 0) / (recentBars.length - 1);
        
        const volumeSurge = currentBar.volume > avgVolume * 2;
        const priceChange = Math.abs(currentBar.close - currentBar.open) / currentBar.open;
        const priceSurge = priceChange > 0.02;
        
        if (volumeSurge && priceSurge) {
            const surgeElement = document.getElementById(`surge-${symbol}`);
            if (surgeElement) {
                surgeElement.textContent = '突破';
                surgeElement.className = 'ml-2 font-medium text-yellow-400';
                
                setTimeout(() => {
                    surgeElement.textContent = '';
                    surgeElement.className = 'ml-2 font-medium';
                }, 3000);
            }
            
            try {
                const markers = [{
                    time: currentBar.time,
                    position: 'aboveBar',
                    color: '#f23645',
                    shape: 'arrowUp',
                    text: '突破'
                }];
                
                if (chartInfo.surgeMarkers && chartInfo.surgeMarkers.series) {
                    chartInfo.surgeMarkers.series.setMarkers(markers);
                }
            } catch (error) {
                console.warn(`设置价格突破标记失败 (NPM版本) ${symbol}:`, error);
            }
        }
    }

    /**
     * 格式化交易量，添加单位（K、M、G），保留1位小数点
     * @param {number} volume 累计成交量
     * @returns {string} 格式化后的字符串，例如 "123.4K", "1.2M", "123.4G"
     */
    formatVolume(volume) {
        if (volume === null || volume === undefined) {
            return '--';
        }
        if (volume >= 1_000_000_000) {  // 大于等于十亿
            return `${(volume / 1_000_000_000).toFixed(1)}G`;
        } else if (volume >= 1_000_000) {  // 大于等于百万
            return `${(volume / 1_000_000).toFixed(1)}M`;
        } else if (volume >= 1_000) {  // 大于等于千
            return `${(volume / 1_000).toFixed(1)}K`;
        } else {
            return `${volume}`;
        }
    }
    
    /**
     * 更新交易次数排名和颜色
     * 获取所有股票的交易次数，对前三名设置不同颜色
     * @param {Object} transactionData 所有股票的原始交易次数数据 {symbol: count}
     */
    updateTransactionsRanking(transactionData) {
        if (!transactionData) return;

        // 收集所有股票的交易次数并排序
        const rankings = [];
        
        // 遍历所有已创建的图表
        this.charts.forEach((chartInfo, symbol) => {
            const transactionEl = document.getElementById(`transactions-${symbol}`);
            if (transactionEl && transactionData[symbol] !== undefined) {
                const count = transactionData[symbol];
                rankings.push({
                    symbol: symbol,
                    count: count,
                    element: transactionEl
                });
            }
        });
        
        // 按交易次数降序排序
        rankings.sort((a, b) => b.count - a.count);
        
        // 更新显示文本和颜色
        rankings.forEach((item, index) => {
            // 格式化交易次数
            const formattedCount = this.formatVolume(item.count);
            item.element.textContent = formattedCount;

            // 设置颜色
            if (index === 0) {
                // 第一名：极亮的绿色
                item.element.className = 'font-mono';
                item.element.style.color = '#39FF14'; // 极亮荧光绿
            } else if (index === 1) {
                // 第二名：橙色 (#FF8C00)
                item.element.className = 'font-mono';
                item.element.style.color = '#FF8C00';
            } else if (index === 2) {
                // 第三名：显眼的红色
                item.element.className = 'font-mono';
                item.element.style.color = '#FF0000';
            } else {
                // 其他：与"交易次数:"文字一致
                item.element.className = 'font-mono text-gray-400';
                item.element.style.color = '';
            }
        });
    }

    /**
     * 获取图表实例
     * @param {string} symbol 股票代码
     * @returns {Object|null} 图表实例
     */
    getChart(symbol) {
        return this.charts.get(symbol) || null;
    }
    
    /**
     * 获取图表数据
     * @param {string} symbol 股票代码
     * @returns {Array} 图表数据
     */
    getChartData(symbol) {
        return this.chartData.get(symbol) || [];
    }
    
    /**
     * 调整图表大小
     * @param {string} symbol 股票代码
     */
    resizeChart(symbol) {
        const chartInfo = this.charts.get(symbol);
        if (chartInfo) {
            const container = chartInfo.container;
            chartInfo.chart.applyOptions({
                width: container.offsetWidth,
                height: container.offsetHeight
            });
            // 保持 rightOffset=2（官方推荐用 applyOptions）
            chartInfo.chart.applyOptions({ timeScale: { rightOffset: 2 } });
        }
    }
    
    /**
     * 调整所有图表大小
     */
    resizeAllCharts() {
        this.charts.forEach((chartInfo, symbol) => {
            this.resizeChart(symbol);
        });
    }
    
    /**
     * 销毁指定股票的图表
     * @param {string} symbol - 股票代码
     */
    destroyChart(symbol) {
        const chartInfo = this.charts.get(symbol);
        if (chartInfo) {
            // 断开 ResizeObserver
            if (chartInfo.resizeObserver && chartInfo.container) {
                try {
                    chartInfo.resizeObserver.disconnect();
                } catch (e) {
                    console.warn('断开ResizeObserver失败:', e);
                }
            }
            chartInfo.chart.remove();
            this.charts.delete(symbol);
            this.chartData.delete(symbol);
            this.updateQueues.delete(symbol);
            // 清理荧光边框效果
            const containerEl = document.getElementById(`chart-container-${symbol}`);
            if (containerEl) {
                containerEl.classList.remove('glow-green', 'glow-blue', 'glow-none');
            }
            // console.log(`销毁图表 (NPM ES模块): ${symbol}`);
        }
    }
    
    /**
     * 销毁所有图表
     */
    destroyAllCharts() {
        for (const symbol of this.charts.keys()) {
            this.destroyChart(symbol);
        }
        // console.log('所有图表已销毁 (NPM ES模块)');
    }
    
    /**
     * 将1分钟数据聚合为5分钟K线数据
     * @param {Array} data 1分钟数据
     * @returns {Array} 5分钟K线数据
     */
    aggregateTo5MinData(data) {
        if (!data || data.length === 0) {
            return [];
        }

        // 按时间排序
        const sortedData = [...data].sort((a, b) => a.time - b.time);

        const aggregated = [];
        let currentCandle = null;
        let skippedVolumeZero = 0;
        let skippedInvalid = 0;
        let lastTime = null;
        let timeJumps = 0;

        for (const point of sortedData) {
            // 检查时间戳连续性
            if (lastTime !== null && point.time - lastTime !== 60) {
                timeJumps++;
            }
            lastTime = point.time;

            // 检查无效数据
            if (
                typeof point.open !== 'number' ||
                typeof point.high !== 'number' ||
                typeof point.low !== 'number' ||
                typeof point.close !== 'number' ||
                typeof point.volume !== 'number' ||
                typeof point.time !== 'number' ||
                !isFinite(point.open) ||
                !isFinite(point.high) ||
                !isFinite(point.low) ||
                !isFinite(point.close) ||
                !isFinite(point.volume) ||
                !isFinite(point.time)
            ) {
                skippedInvalid++;
                continue;
            }

            // 跳过成交量为 0 的数据，视为熔断期间
            if (!point.volume || point.volume === 0) {
                skippedVolumeZero++;
                currentCandle = null; // 强制结束当前K线，进入断层
                continue;
            }

            const periodStart = Math.floor(point.time / 300) * 300;

            if (!currentCandle || currentCandle.time !== periodStart) {
                // 新的5分钟K线开始
                currentCandle = {
                    time: periodStart,
                    open: point.open,
                    high: point.high,
                    low: point.low,
                    close: point.close,
                    volume: point.volume,
                };
                aggregated.push(currentCandle);
            } else {
                // 聚合到当前K线
                currentCandle.high = Math.max(currentCandle.high, point.high);
                currentCandle.low = Math.min(currentCandle.low, point.low);
                currentCandle.close = point.close;
                currentCandle.volume += point.volume;
            }
        }

        return aggregated;
    }

    /**
     * 价格突破检测指标（与参考文件完全一致）
     * @param {Array} data 5分钟K线数据
     * @returns {{signals: Array, lines: Array}}
     */
    calculatePriceSurgeDetector(data) {
        if (!data || data.length === 0) return { signals: [], lines: [] };

        const signals = [];
        const lines = [];

        // 状态变量 - 回到原始的单一形态追踪，但改进失效检查逻辑
        let surgePrice = null;
        let surgeBar = null;
        let surgeLow = null;
        let downCount = 0;
        let inTracking = false;
        let waitingDown = false;

        for (let i = 1; i < data.length; i++) {
            const current = data[i];
            const previous = data[i - 1];

            // 计算价格变化百分比
            const priceChangePercent = ((current.close - previous.close) / previous.close) * 100;

            // 判断K线类型
            const isDownCandle = current.close < current.open;
            const isUpCandle = current.close > current.open;

            // 检测突破条件：涨幅超过10%且不在追踪状态
            if (priceChangePercent >= 10 && !inTracking) {
                // console.log(`[新突破] 涨幅 ${priceChangePercent.toFixed(1)}%，开始追踪形态`);
                surgePrice = current.open;
                surgeBar = i;
                surgeLow = current.low;
                downCount = 0;
                inTracking = true;
                waitingDown = true;

                // 添加突破信号（仅用文字）
                signals.push({
                    time: current.time,
                    position: 'belowBar',
                    color: '#00ff00',
                    shape: 'arrowUp',
                    text: `突破${priceChangePercent.toFixed(1)}%`,
                    surgePrice: current.open,  // 记录突破价格用于后续失效检查
                    surgeLow: current.low,     // 记录最低价用于后续失效检查
                    surgeHigh: current.high,   // 记录最高价用于跌破检查
                    surgeClose: current.close, // 记录收盘价用于跌破检查
                    surgeId: `surge_${current.time}_${i}`,  // 唯一标识符
                    isValid: true
                });
            }

            // 形态追踪逻辑
            if (inTracking) {
                if (waitingDown) {
                    // 跳过突破K线，从下一根开始计数
                    waitingDown = false;
                } else {
                    if (isDownCandle) {
                        // 检查是否跌破突破阳线最低价
                        if (current.low < surgeLow) {
                            // 形态失效 - 直接删除当前追踪的突破信号
                            // console.log(`[形态失效] 跌破突破最低价 ${surgeLow}，当前最低价 ${current.low}`);
                            for (let j = signals.length - 1; j >= 0; j--) {
                                if (signals[j].text.includes('突破') && signals[j].surgeLow === surgeLow) {
                                    signals.splice(j, 1);  // 直接删除信号
                                    break;
                                }
                            }

                            // 重置状态变量
                            surgePrice = null;
                            surgeBar = null;
                            surgeLow = null;
                            downCount = 0;
                            inTracking = false;
                            waitingDown = false;
                        } else {
                            downCount++;
                            if (downCount > 4) {
                                // 阴线过多，形态失效 - 直接删除当前追踪的突破信号
                                // console.log(`[形态失效] 阴线过多 ${downCount} 根，超过4根限制`);
                                for (let j = signals.length - 1; j >= 0; j--) {
                                    if (signals[j].text.includes('突破') && signals[j].surgeLow === surgeLow) {
                                        signals.splice(j, 1);  // 直接删除信号
                                        break;
                                    }
                                }

                                // 重置状态变量
                                surgePrice = null;
                                surgeBar = null;
                                surgeLow = null;
                                downCount = 0;
                                inTracking = false;
                                waitingDown = false;
                            }
                        }
                    } else if (isUpCandle) {
                        if (downCount >= 1 && downCount <= 4) {
                            // 出现止跌阳线，且阴线数量在1-4根之间，形态有效
                            lines.push({
                                startTime: data[surgeBar].time,
                                startPrice: surgePrice,
                                endTime: current.time,
                                endPrice: surgePrice,
                                color: '#aab3f9',
                                width: 3,
                                style: 'dashed'
                            });

                            // 标记对应的突破信号为已完成（防止被历史失效检查删除）
                            for (let j = 0; j < signals.length; j++) {
                                if (signals[j].text.includes('突破') && signals[j].surgeLow === surgeLow) {
                                    signals[j].isCompleted = true; // 标记为已完成形态
                                    signals[j].breakLow = data[surgeBar].low; // 保存最后一根突破K线的最低价
                                    // console.log(`[形态完成] 突破标记已完成，突破K线最低价: ${data[surgeBar].low}`);
                                    break;
                                }
                            }

                            // 添加形态完成信号
                            signals.push({
                                time: current.time,
                                position: 'aboveBar',
                                color: '#ff6b6b',
                                shape: 'circle',
                                text: `形态${downCount}`,
                                confirmId: `confirm_${current.time}_${i}`,  // 唯一标识符
                                isValid: true,
                                breakLow: data[surgeBar].low  // 保存最后一根突破K线的最低价
                            });
                        } else if (downCount === 0) {
                            // 突破K线后直接是阳线，形态失效 - 直接删除突破信号
                            // console.log(`[形态失效] 突破后直接出现阳线，形态无效`);
                            for (let j = signals.length - 1; j >= 0; j--) {
                                if (signals[j].text.includes('突破') && signals[j].surgeLow === surgeLow) {
                                    signals.splice(j, 1);  // 直接删除信号
                                    break;
                                }
                            }
                        }

                        // 形态完成或失效，重置状态变量（与Pine Script一致：不重置surgeLow）
                        surgePrice = null;
                        surgeBar = null;
                        // 注意：Pine Script中没有重置surge_low，这里保持一致
                        // surgeLow = null;  // 不重置，保持与Pine Script一致
                        downCount = 0;
                        inTracking = false;
                        waitingDown = false;
                    }
                }
            }

            // 检查所有历史突破信号的失效条件（跳过已完成的形态）
            if (isDownCandle) {
                for (let j = signals.length - 1; j >= 0; j--) {
                    const signal = signals[j];
                    if (signal.text.includes('突破') && signal.isValid && signal.surgeLow && !signal.isCompleted) {
                        // 只检查未完成形态的失效条件
                        if (current.low < signal.surgeLow) {
                            // console.log(`[突破失效] 价格 ${current.low} 跌破突破最低价 ${signal.surgeLow}，移除标记: ${signal.text}`);
                            signals.splice(j, 1);  // 直接删除失效的历史突破信号
                        }
                    }
                }
            }
        }

        // 确保所有形态完成标记都保存了突破K线最低价
        signals.forEach(signal => {
            // 如果是形态完成标记，确保有突破K线价格信息
            if (signal.text && signal.text.includes('形态') && !signal.breakLow) {
                // console.log(`[属性修复] 形态完成标记缺少breakLow，尝试补充`);
                // 这种情况不应该发生，但作为容错措施
            }
            
            // 保存其他需要的属性（但删除TradingView不需要的）
            delete signal.isValid;
            delete signal.isCompleted;
            delete signal.surgePrice;
            delete signal.surgeLow;
            delete signal.surgeHigh;
            delete signal.surgeClose;
            delete signal.surgeId;
            delete signal.confirmId;
            delete signal.lastBreakthroughLow;
            delete signal.completed;
        });

        return { signals, lines };
    }

    /**
     * 检查形态完成后是否跌破最后一根突破K线
     * @param {Array} signals 突破信号数组
     * @param {Object} currentCandle 当前K线数据
     * @returns {boolean} 是否应该清除形态文字
     */
    checkBreakdownAfterCompletion(signals, currentCandle) {
        if (!signals || signals.length === 0 || !currentCandle) {
            return false;
        }

        // 查找所有确认标记（✓），表示有形态完成
        const confirmationSignals = signals.filter(s => s.text && s.text.includes('形态'));
        
        if (confirmationSignals.length === 0) {
            return false; // 没有确认标记，说明形态未完成
        }

        // 获取最后一个确认标记
        const lastConfirmation = confirmationSignals[confirmationSignals.length - 1];
        
        // 直接从确认标记中获取保存的突破K线最低价
        if (lastConfirmation.breakLow !== undefined) {
            // 比较当前K线最低价和突破K线最低价
            if (currentCandle.low < lastConfirmation.breakLow) {
                // console.log(`[形态跌破] 当前低点 ${currentCandle.low} 跌破突破最低价 ${lastConfirmation.breakLow}`);
                return true;
            }
        }

        return false;
    }

    /**
     * 优化的数据变化检测
     * @param {Array} newData 新数据
     * @param {Array} oldData 旧数据
     * @returns {Array} 变化的时间戳数组
     */
    getChanged1MinTimesOptimized(newData, oldData) {
        // 使用Map提高查找效率
        const oldMap = new Map();
        const newMap = new Map();
        
        // 生成数据哈希，避免逐字段比较
        const generateHash = (d) => `${d.open}-${d.high}-${d.low}-${d.close}-${d.volume}`;
        
        oldData.forEach(d => oldMap.set(d.time, generateHash(d)));
        newData.forEach(d => newMap.set(d.time, generateHash(d)));
        
        const changed = [];
        
        // 检查新增和修改的数据
        for (const [time, hash] of newMap) {
            if (!oldMap.has(time) || oldMap.get(time) !== hash) {
                changed.push(time);
            }
        }
        
        return changed;
    }

    /**
     * 批量更新5分钟K线数据
     * @param {Array} fiveMinArr 现有5分钟K线数组
     * @param {Array} newCandles 新的5分钟K线数组
     * @returns {Array} 更新后的5分钟K线数组
     */
    batchUpdate5MinCandles(fiveMinArr, newCandles) {
        if (!newCandles.length) return fiveMinArr;
        
        // 按时间排序新数据
        const sortedNew = [...newCandles].sort((a, b) => a.time - b.time);
        
        // 批量合并，减少单个插入操作
        let mergedArr = [];
        let oldIndex = 0, newIndex = 0;
        
        while (oldIndex < fiveMinArr.length && newIndex < sortedNew.length) {
            const oldCandle = fiveMinArr[oldIndex];
            const newCandle = sortedNew[newIndex];
            
            if (oldCandle.time < newCandle.time) {
                mergedArr.push(oldCandle);
                oldIndex++;
            } else if (oldCandle.time > newCandle.time) {
                mergedArr.push(newCandle);
                newIndex++;
            } else {
                // 相同时间，合并数据但保持原有开盘价
                const mergedCandle = {
                    ...newCandle,
                    open: oldCandle.open  // 保持原有开盘价
                };
                mergedArr.push(mergedCandle);
                oldIndex++;
                newIndex++;
            }
        }
        
        // 添加剩余数据
        while (oldIndex < fiveMinArr.length) {
            mergedArr.push(fiveMinArr[oldIndex++]);
        }
        while (newIndex < sortedNew.length) {
            mergedArr.push(sortedNew[newIndex++]);
        }
        
        return mergedArr;
    }

    /**
     * 局部聚合单个period的5分钟K线（增强版）
     * @param {Array} oneMinArr 1分钟K线数组
     * @param {Object} existingCandle 已存在的5分钟K线（可选）
     * @returns {Object|null} 聚合后的5分钟K线
     */
    aggregateOnePeriodEnhanced(oneMinArr, existingCandle = null) {
        if (!oneMinArr.length) return null;

        // 验证和清洗数据，跳过成交量为0的数据
        const validData = oneMinArr.filter(d =>
            d && typeof d.time === 'number' &&
            typeof d.open === 'number' &&
            typeof d.high === 'number' &&
            typeof d.low === 'number' &&
            typeof d.close === 'number' &&
            typeof d.volume === 'number' &&
            isFinite(d.open) && isFinite(d.high) &&
            isFinite(d.low) && isFinite(d.close) &&
            d.volume > 0 // 跳过成交量为0的数据
        );

        if (!validData.length) return null;

        const sorted = validData.sort((a, b) => a.time - b.time);
        const periodStart = Math.floor(sorted[0].time / 300) * 300;

        return {
            time: periodStart,
            open: existingCandle ? existingCandle.open : sorted[0].open, // 如果已存在K线，保持原开盘价
            high: Math.max(...sorted.map(d => d.high)),
            low: Math.min(...sorted.map(d => d.low)),
            close: sorted[sorted.length - 1].close,
            volume: sorted.reduce((sum, d) => sum + d.volume, 0)
        };
    }

    /**
     * 智能图表更新策略
     * @param {string} symbol 股票代码
     * @param {Array} fiveMinData 5分钟K线数据
     * @param {Set} changedPeriods 变化的时间周期集合
     */
    updateChartSmart(symbol, fiveMinData, changedPeriods) {
        const chartInfo = this.charts.get(symbol);
        if (!chartInfo) return;
        
        try {
            if (changedPeriods.size === 0) {
                return; // 无变化，跳过更新
            }
            
            if (changedPeriods.size <= 5 && 
                changedPeriods.has(fiveMinData[fiveMinData.length - 1]?.time)) {
            // if (changedPeriods.has(fiveMinData[fiveMinData.length - 1]?.time)) {
                // 只有最后一根K线变化，使用update
                const lastCandle = fiveMinData[fiveMinData.length - 1];
                if (lastCandle.open === lastCandle.close) {
                    lastCandle.color = this.seriesOptions.downColor;
                }
                chartInfo.series.update(lastCandle);
                console.log(`更新K线 (updateChartSmart): ${symbol}`, lastCandle);
            } else {
                // console.log(`更新K线 setData: ${symbol}`, fiveMinData);
                // 多根K线变化，使用setData
                // open=close时用阴线色
                const downColor = this.seriesOptions.downColor;
                const coloredData = fiveMinData.map(item => {
                    if (item.open === item.close) {
                        return { ...item, color: downColor }; 
                    }
                    return item;
                });
                // 更新K线图表
                chartInfo.series.setData(coloredData);
                console.log(`全量更新K线图表 (updateChartSmart): ${symbol}`, coloredData);
            }
            
            // 增量更新技术指标
            this.updateTechnicalIndicatorsIncremental(symbol, fiveMinData, changedPeriods);
            
        } catch (error) {
            console.error(`图表更新失败: ${symbol}`, error);
            // Fallback到全量更新
            chartInfo.series.setData(fiveMinData);
        }
    }

    /**
     * 增量更新技术指标
     * @param {string} symbol 股票代码
     * @param {Array} fiveMinData 5分钟K线数据
     * @param {Set} changedPeriods 变化的时间周期集合
     */
    updateTechnicalIndicatorsIncremental(symbol, fiveMinData, changedPeriods) {
        const chartInfo = this.charts.get(symbol);
        if (!chartInfo || fiveMinData.length === 0) return;

        // 如果变化范围较大，直接全量重算
        if (changedPeriods.size > 10) {
            // 计算EMA指标
            const ema9 = this.calculateEMA(fiveMinData, 9);
            const ema20 = this.calculateEMA(fiveMinData, 20);
            const ema120 = this.calculateEMA(fiveMinData, 120);
            
            // 更新EMA系列
            if (chartInfo.ema9Series) chartInfo.ema9Series.setData(ema9);
            if (chartInfo.ema20Series) chartInfo.ema20Series.setData(ema20);
            if (chartInfo.ema120Series) chartInfo.ema120Series.setData(ema120);

            // 计算MACD指标
            const macdResult = this.calculateMACD(fiveMinData);
            
            // 更新MACD系列
            if (chartInfo.macdLineSeries) chartInfo.macdLineSeries.setData(macdResult.macdLine);
            if (chartInfo.signalLineSeries) chartInfo.signalLineSeries.setData(macdResult.signalLine);
            if (chartInfo.macdHistSeries) chartInfo.macdHistSeries.setData(macdResult.hist);
        } else {
            // 小范围变化，进行增量更新（当前使用全量重算作为安全实现）
            // TODO: 实现真正的增量技术指标计算
            const ema9 = this.calculateEMA(fiveMinData, 9);
            const ema20 = this.calculateEMA(fiveMinData, 20);
            const ema120 = this.calculateEMA(fiveMinData, 120);
            
            if (chartInfo.ema9Series) chartInfo.ema9Series.setData(ema9);
            if (chartInfo.ema20Series) chartInfo.ema20Series.setData(ema20);
            if (chartInfo.ema120Series) chartInfo.ema120Series.setData(ema120);

            const macdResult = this.calculateMACD(fiveMinData);
            if (chartInfo.macdLineSeries) chartInfo.macdLineSeries.setData(macdResult.macdLine);
            if (chartInfo.signalLineSeries) chartInfo.signalLineSeries.setData(macdResult.signalLine);
            if (chartInfo.macdHistSeries) chartInfo.macdHistSeries.setData(macdResult.hist);
        }

        // 重要：重新计算突破检测标记
        const surgeData = this.calculatePriceSurgeDetector(fiveMinData);
        
        // 设置突破标记（使用正确的API）
        if (chartInfo.surgeMarkers && chartInfo.surgeMarkers.plugin) {
            try {
                chartInfo.surgeMarkers.plugin.setMarkers(surgeData.signals);
                if (surgeData.signals.length > 0) {
                    // console.log(`[增量更新] ${symbol} 已设置 ${surgeData.signals.length} 个突破标记`);
                }
            } catch (error) {
                console.error(`[增量更新] ${symbol} 标记插件设置失败:`, error);
            }
        }

        // 更新形态文字显示
        this.updateSurgeTextDisplay(symbol, surgeData.signals, fiveMinData);
    }

    /**
     * 更新形态文字显示
     * @param {string} symbol 股票代码
     * @param {Array} signals 突破信号数组
     * @param {Array} fiveMinData 5分钟K线数据
     */
    updateSurgeTextDisplay(symbol, signals, fiveMinData) {
        let shapeText = '';
        
        if (signals.length > 0) {
            const latestSignal = signals[signals.length - 1];
            if (latestSignal.text.includes('突破')) {
                shapeText = '突破';
            } else if (latestSignal.text.includes('形态')) {
                shapeText = '形态';
            }
        }
        
        // 检查形态完成后是否跌破最后一根突破K线
        if (fiveMinData.length > 0) {
            const currentCandle = fiveMinData[fiveMinData.length - 1];
            const shouldClearText = this.checkBreakdownAfterCompletion(signals, currentCandle);
            
            if (shouldClearText) {
                // console.log(`[形态跌破] ${symbol} 检测到跌破，清除形态文字`);
                shapeText = '';
            }
        }
        
        const surgeEl = document.getElementById(`surge-${symbol}`);
        const containerEl = document.getElementById(`chart-container-${symbol}`);
        
        if (surgeEl) {
            const previousText = surgeEl.textContent;
            surgeEl.textContent = shapeText;
            
            // 设置样式类
            if (shapeText.includes('突破')) {
                surgeEl.className = 'ml-2 font-medium text-green-400';
                // 为容器添加绿色荧光边框效果
                if (containerEl) {
                    containerEl.classList.remove('glow-blue', 'glow-none');
                    containerEl.classList.add('glow-green');
                }
            } else if (shapeText.includes('形态')) {
                surgeEl.className = 'ml-2 font-medium text-blue-400';
                // 为容器添加蓝色荧光边框效果
                if (containerEl) {
                    containerEl.classList.remove('glow-green', 'glow-none');
                    containerEl.classList.add('glow-blue');
                }
            } else {
                surgeEl.className = 'ml-2 font-medium';
                // 移除荧光边框效果，恢复默认状态
                if (containerEl) {
                    containerEl.classList.remove('glow-green', 'glow-blue');
                    containerEl.classList.add('glow-none');
                }
            }
        }
    }

    /**
     * 增量更新图表数据
     * @param {string} symbol 股票代码
     * @param {Array} newOneMinData 新的1分钟K线数据
     */
    updateChartIncremental(symbol, newOneMinData) {
        // 注释掉性能监控
        // this.performanceMonitor.startTiming('incremental_update', symbol);
        
        try {
            // 检查是否为空数据
            if (!newOneMinData || newOneMinData.length === 0) {
                return;
            }

            // 1. 数据验证和清洗
            const validData = this.validateAndCleanData(newOneMinData);
            
            // 2. 检测变化
            const oldData = this.oneMinData.get(symbol) || [];
            
            const changedTimes = this.getChanged1MinTimesOptimized(validData, oldData);
            
            if (changedTimes.length === 0) {
                // console.log(`[增量更新] ${symbol}: 无数据变化，跳过更新`);
                return; // 无变化
            }
            
            // 3. 计算受影响的5分钟区间
            const affectedPeriods = new Set(
                changedTimes.map(time => Math.floor(time / 300) * 300)
            );
            
            // 4. 局部聚合
            const oldFiveMin = this.fiveMinData.get(symbol) || [];
            const newFiveMinCandles = [];
            
            for (const period of affectedPeriods) {
                const oneMinInPeriod = validData.filter(d =>
                    Math.floor(d.time / 300) * 300 === period
                );
                // 查找已存在的5分钟K线
                const existingCandle = oldFiveMin.find(candle => candle.time === period);
                // 局部聚合，传入已存在的K线以保持开盘价
                const aggregated = this.aggregateOnePeriodEnhanced(oneMinInPeriod, existingCandle);
                // 聚合后的5分钟K线数据
                if (aggregated) {
                    newFiveMinCandles.push(aggregated);
                }
            }
            
            // 5. 更新缓存
            const updatedFiveMin = this.batchUpdate5MinCandles(oldFiveMin, newFiveMinCandles);
            
            // 6. 更新图表
            this.updateChartSmart(symbol, updatedFiveMin, affectedPeriods);
            
            // 7. 保存数据
            this.oneMinData.set(symbol, validData);
            this.fiveMinData.set(symbol, updatedFiveMin);
            this.chartData.set(symbol, updatedFiveMin); // 保持与现有接口兼容
            
            // 8. 清理旧数据
            this.dataManager.cleanupOldData(symbol, this);
            
        } catch (error) {
            console.error(`增量更新失败: ${symbol}`, error);
            // Fallback到原有的全量更新
            this.updateChart(symbol, newOneMinData);
        } finally {
            // this.performanceMonitor.endTiming('incremental_update', symbol);
        }
    }

    /**
     * 数据验证和清洗
     * @param {Array} data K线数据
     * @returns {Array} 清洗后的数据
     */
    validateAndCleanData(data) {
        if (!Array.isArray(data)) return [];
        
        return data.filter(item => {
            // 检查必要字段
            if (!item || typeof item.time !== 'number' || item.time <= 0) return false;
            if (typeof item.open !== 'number' || typeof item.high !== 'number' || 
                typeof item.low !== 'number' || typeof item.close !== 'number') return false;
            
            // 检查价格合理性
            if (item.high < item.low || item.open < 0 || item.close < 0) return false;
            if (item.high < Math.max(item.open, item.close) || 
                item.low > Math.min(item.open, item.close)) return false;
            
            // 检查数据有效性
            if (!isFinite(item.open) || !isFinite(item.high) || 
                !isFinite(item.low) || !isFinite(item.close)) return false;
                
            return true;
        }).sort((a, b) => a.time - b.time); // 确保时间排序
    }
    
    /**
     * 启用或禁用增量更新
     * @param {boolean} enabled 是否启用
     */
    setIncrementalUpdateEnabled(enabled) {
        this.incrementalUpdateEnabled = enabled;
        console.log(`增量更新已${enabled ? '启用' : '禁用'}`);
    }

    /**
     * 获取增量更新统计信息
     * @param {string} symbol 股票代码
     * @returns {Object} 统计信息
     */
    getIncrementalStats(symbol) {
        const oneMinCount = (this.oneMinData.get(symbol) || []).length;
        const fiveMinCount = (this.fiveMinData.get(symbol) || []).length;
        const chartDataCount = (this.chartData.get(symbol) || []).length;
        
        return {
            oneMinuteData: oneMinCount,
            fiveMinuteData: fiveMinCount,
            chartData: chartDataCount,
            incrementalEnabled: this.incrementalUpdateEnabled
        };
    }

    /**
     * 强制清理指定股票的缓存数据
     * @param {string} symbol 股票代码
     */
    clearCacheData(symbol) {
        this.oneMinData.delete(symbol);
        this.fiveMinData.delete(symbol);
        this.lastUpdateHash.delete(symbol);
        console.log(`已清理 ${symbol} 的缓存数据`);
    }

    /**
     * 获取内存使用情况
     * @returns {Object} 内存统计
     */
    getMemoryStats() {
        const symbols = Array.from(this.charts.keys());
        let totalOneMin = 0;
        let totalFiveMin = 0;
        
        symbols.forEach(symbol => {
            totalOneMin += (this.oneMinData.get(symbol) || []).length;
            totalFiveMin += (this.fiveMinData.get(symbol) || []).length;
        });
        
        return {
            totalSymbols: symbols.length,
            totalOneMinuteCandles: totalOneMin,
            totalFiveMinuteCandles: totalFiveMin,
            estimatedMemoryMB: Math.round((totalOneMin + totalFiveMin) * 0.001) // 粗略估算
        };
    }

    /**
     * 监控内存使用情况
     * 提供详细的内存信息，包括浏览器堆内存使用情况
     * @param {boolean} showConsoleLog - 是否在控制台显示信息
     * @returns {Object} 详细的内存监控信息
     */
    monitorMemoryUsage(showConsoleLog = false) {
        const memoryInfo = {
            timestamp: new Date().toISOString(),
            charts: {
                totalSymbols: this.charts.size,
                symbols: Array.from(this.charts.keys())
            },
            data: {
                oneMinuteData: 0,
                fiveMinuteData: 0,
                chartData: 0,
                totalDataPoints: 0
            },
            browser: {
                available: false,
                usedJSHeapSize: 0,
                totalJSHeapSize: 0,
                jsHeapSizeLimit: 0,
                usedMB: 0,
                totalMB: 0,
                limitMB: 0,
                usagePercentage: 0
            },
            maps: {
                chartsMapSize: this.charts.size,
                chartDataMapSize: this.chartData.size,
                oneMinDataMapSize: this.oneMinData.size,
                fiveMinDataMapSize: this.fiveMinData.size,
                updateQueuesMapSize: this.updateQueues.size,
                lastUpdateHashMapSize: this.lastUpdateHash.size
            },
            recommendations: []
        };

        // 统计数据量
        this.charts.forEach((chartInfo, symbol) => {
            const oneMinData = this.oneMinData.get(symbol) || [];
            const fiveMinData = this.fiveMinData.get(symbol) || [];
            const chartData = this.chartData.get(symbol) || [];
            
            memoryInfo.data.oneMinuteData += oneMinData.length;
            memoryInfo.data.fiveMinuteData += fiveMinData.length;
            memoryInfo.data.chartData += chartData.length;
        });
        
        memoryInfo.data.totalDataPoints = 
            memoryInfo.data.oneMinuteData + 
            memoryInfo.data.fiveMinuteData + 
            memoryInfo.data.chartData;

        // 获取浏览器内存信息
        if (performance && performance.memory) {
            memoryInfo.browser.available = true;
            memoryInfo.browser.usedJSHeapSize = performance.memory.usedJSHeapSize;
            memoryInfo.browser.totalJSHeapSize = performance.memory.totalJSHeapSize;
            memoryInfo.browser.jsHeapSizeLimit = performance.memory.jsHeapSizeLimit;
            
            // 转换为MB
            memoryInfo.browser.usedMB = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
            memoryInfo.browser.totalMB = Math.round(performance.memory.totalJSHeapSize / 1024 / 1024);
            memoryInfo.browser.limitMB = Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024);
            
            // 计算使用率
            memoryInfo.browser.usagePercentage = Math.round(
                (performance.memory.usedJSHeapSize / performance.memory.jsHeapSizeLimit) * 100
            );
        }

        // 生成建议
        if (memoryInfo.browser.available) {
            if (memoryInfo.browser.usagePercentage > 80) {
                memoryInfo.recommendations.push('内存使用率超过80%，建议清理历史数据');
            }
            if (memoryInfo.browser.usagePercentage > 90) {
                memoryInfo.recommendations.push('内存使用率超过90%，强烈建议立即清理数据');
            }
        }
        
        if (memoryInfo.data.totalDataPoints > 50000) {
            memoryInfo.recommendations.push('数据点总数超过50000，建议启用数据清理');
        }
        
        if (memoryInfo.charts.totalSymbols > 20) {
            memoryInfo.recommendations.push('图表数量较多，注意监控内存使用');
        }

        // 控制台输出
        if (showConsoleLog) {
            console.group('📊 内存使用监控');
            console.log('时间:', memoryInfo.timestamp);
            console.log('图表数量:', memoryInfo.charts.totalSymbols);
            console.log('数据统计:', {
                '1分钟数据': memoryInfo.data.oneMinuteData,
                '5分钟数据': memoryInfo.data.fiveMinuteData,
                '图表数据': memoryInfo.data.chartData,
                '总数据点': memoryInfo.data.totalDataPoints
            });
            
            if (memoryInfo.browser.available) {
                console.log('浏览器内存:', {
                    '已用': `${memoryInfo.browser.usedMB}MB`,
                    '总计': `${memoryInfo.browser.totalMB}MB`,
                    '限制': `${memoryInfo.browser.limitMB}MB`,
                    '使用率': `${memoryInfo.browser.usagePercentage}%`
                });
            } else {
                console.warn('浏览器不支持 performance.memory API');
            }
            
            if (memoryInfo.recommendations.length > 0) {
                console.warn('建议:', memoryInfo.recommendations);
            }
            
            console.groupEnd();
        }

        return memoryInfo;
    }

    /**
     * 生成数据哈希，用于调试比较
     * @param {Object} data 数据点
     * @returns {string} 哈希字符串
     */
    generateDataHash(data) {
        return `${data.open}-${data.high}-${data.low}-${data.close}-${data.volume}`;
    }

    /**
     * 设置回放参数
     * @param {Object} settings 回放设置
     */
    setPlaybackSettings(settings) {
        this.playbackSettings = {...this.playbackSettings, ...settings};
    }
}

/**
 * 性能监控类
 */
class PerformanceMonitor {
    constructor() {
        this.metrics = new Map();
    }
    
    startTiming(operation, symbol) {
        const key = `${operation}_${symbol}`;
        this.metrics.set(key, { start: performance.now(), operation, symbol });
    }
    
    endTiming(operation, symbol) {
        const key = `${operation}_${symbol}`;
        const metric = this.metrics.get(key);
        if (metric) {
            const duration = performance.now() - metric.start;
            if (duration > 10) { // 只记录超过10ms的操作
                console.log(`[性能] ${operation} ${symbol}: ${duration.toFixed(2)}ms`);
            }
            this.metrics.delete(key);
            return duration;
        }
    }
}

/**
 * 数据管理类
 */
class DataManager {
    constructor() {
        this.maxHistoryDays = 30; // 保留最近30天数据
        this.maxCandleCount = 10000; // 每个symbol最多保留10000根K线
    }
    
    cleanupOldData(symbol, chartManager) {
        const oneMinData = chartManager.oneMinData.get(symbol) || [];
        const fiveMinData = chartManager.fiveMinData.get(symbol) || [];
        
        if (oneMinData.length <= this.maxCandleCount && fiveMinData.length <= this.maxCandleCount / 5) {
            return; // 无需清理
        }
        
        // 按时间清理
        const cutoffTime = Date.now() / 1000 - this.maxHistoryDays * 24 * 3600;
        
        const filteredOneMin = oneMinData.filter(d => d.time > cutoffTime);
        const filteredFiveMin = fiveMinData.filter(d => d.time > cutoffTime);
        
        // 按数量清理
        if (filteredOneMin.length > this.maxCandleCount) {
            chartManager.oneMinData.set(symbol, filteredOneMin.slice(-this.maxCandleCount));
        } else {
            chartManager.oneMinData.set(symbol, filteredOneMin);
        }
        
        if (filteredFiveMin.length > this.maxCandleCount / 5) {
            chartManager.fiveMinData.set(symbol, filteredFiveMin.slice(-this.maxCandleCount / 5));
        } else {
            chartManager.fiveMinData.set(symbol, filteredFiveMin);
        }
    }
}
