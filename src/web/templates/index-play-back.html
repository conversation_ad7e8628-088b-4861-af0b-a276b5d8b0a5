<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>多股票实时监控</title>
  <script src="https://cdn.tailwindcss.com"></script>
  
  <!-- 外部CSS样式文件 -->
  <link rel="stylesheet" href="/static/css/main.css">
  
  <!-- 版本偏好管理 -->
  <script src="/static/js/version-preference.js"></script>
  <!-- 性能测试脚本（开发阶段） -->
  <!-- <script src="/static/js/performance_test.js"></script> -->
  
  <!-- 版本指示器样式 -->
  <style>
    .version-indicator {
      position: fixed;
      top: 10px;
      right: 10px;
      background: #16a34a;
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 11px;
      z-index: 1000;
      opacity: 0.8;
    }
    
    /* 确保荧光边框效果样式在此处也定义（以防main.css未加载） */
    .glow-blue {
      display: inline-block;
      border: 2px solid #3b82f6;
      background: #222;
      color: #fff;
      border-radius: 12px;
      padding: 0 8px;
      filter: drop-shadow(0 0 8px #3b82f6) drop-shadow(0 0 20px #3b82f6);
      transition: all 0.3s ease;
      animation: glowBlueFilter 2s infinite alternate;
    }

    .glow-green {
      display: inline-block;
      border: 2px solid #22c55e;
      background: #222;
      color: #fff;
      border-radius: 12px;
      padding: 0 8px;
      filter: drop-shadow(0 0 8px #22c55e) drop-shadow(0 0 20px #22c55e);
      transition: all 0.3s ease;
      animation: glowGreenFilter 2s infinite alternate;
    }

    .glow-none {
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      border: 1px solid transparent;
      transition: all 0.3s ease;
    }

    @keyframes glowBlueFilter {
      from { filter: drop-shadow(0 0 8px #3b82f6); }
      to   { filter: drop-shadow(0 0 20px #3b82f6); }
    }

    @keyframes glowGreenFilter {
      from { filter: drop-shadow(0 0 8px #22c55e); }
      to   { filter: drop-shadow(0 0 20px #22c55e); }
    }
  </style>
</head>
<body class="bg-gray-900 text-gray-100 min-h-screen">
  <!-- 版本指示器 -->
  <!-- <div class="version-indicator">NPM ES Module</div> -->
  
  <!-- 顶部导航栏 -->
  <header class="bg-gray-800 border-b border-gray-700 px-4 py-3">
    <div class="flex items-center justify-between">
      <h1 class="text-xl font-bold text-white">多股票实时监控</h1>
      <div class="flex items-center space-x-6 text-sm">
        <div class="flex items-center space-x-2">
          <span class="text-gray-400">状态:</span>
          <span id="status" class="text-yellow-400">连接中...</span>
        </div>
        <div class="flex items-center space-x-2">
          <span class="text-gray-400">股票数量:</span>
          <span id="stock-count" class="text-blue-400">0</span>
        </div>
        <div class="flex items-center space-x-2">
          <span class="text-gray-400">最后更新:</span>
          <span id="last-update" class="text-green-400 w-16 font-mono inline-block">--</span>
        </div>
        <div class="flex items-center space-x-2">
          <span class="text-gray-400">当前时间:</span>
          <span id="current-time" class="text-gray-300 w-16 font-mono inline-block">--</span>
        </div>
        <!-- 版本切换按钮 -->
        <div class="flex items-center space-x-2">
          <button 
            id="switch-version" 
            class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm transition-colors"
            onclick="window.location.href = window.location.pathname"
          >
            切换到CDN版本
          </button>
        </div>
      </div>
    </div>
  </header>

  <!-- 主要内容区域 -->
  <main class="w-full px-4 py-6">
    <!-- 图表网格 -->
    <div class="mb-6">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-lg font-semibold text-gray-200">实时5分钟K线图</h2>
        
        <!-- 回放控制面板 -->
        <div class="flex items-center space-x-4 text-sm">
          <div class="flex items-center space-x-2">
            <label class="text-gray-300">日期:</label>
            <input type="date" id="playback-date" class="px-2 py-1 rounded bg-gray-700 text-white text-sm" value="2025-07-02">
          </div>
          <div class="flex items-center space-x-2">
            <label class="text-gray-300">开始时间:</label>
            <select id="playback-time" class="px-2 py-1 rounded bg-gray-700 text-white text-sm">
              <option value="09:30">09:30</option>
              <option value="10:00">10:00</option>
              <option value="11:00">11:00</option>
              <option value="14:00">14:00</option>
              <option value="15:00">15:00</option>
            </select>
          </div>
          <div class="flex items-center space-x-2">
            <label class="text-gray-300">速度:</label>
            <select id="playback-speed" class="px-2 py-1 rounded bg-gray-700 text-white text-sm">
              <option value="500">快速 (500ms)</option>
              <option value="1000" selected>正常 (1000ms)</option>
              <option value="2000">慢速 (2000ms)</option>
              <option value="5000">很慢 (5000ms)</option>
            </select>
          </div>
          <button id="start-playback-btn" onclick="handleStartPlayback()" class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm transition-colors">开始回放</button>
          <button id="stop-playback-btn" onclick="handleStopPlayback()" disabled class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm transition-colors disabled:bg-gray-600 disabled:cursor-not-allowed">停止回放</button>
        </div>
      </div>
      
      <div id="charts-grid" class="grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
        <!-- 图表容器将通过NPM ES模块动态生成 -->
      </div>
    </div>
  </main>

  <!-- 回放控制脚本 -->
  <script src="/static/js/playback-control.js"></script>
  
  <!-- 内联回放控制函数 -->
  <script>
    /**
     * 数据回放流转过程:
     * 1. 用户选择日期和时间，点击"开始回放"按钮
     * 2. handleStartPlayback() → startPlayback()
     * 3. startPlayback() 检查数据和实例，然后调用 initializePlaybackData()
     * 4. initializePlaybackData() 预处理数据，创建时间点索引和数据映射
     * 5. 调用 startFrontendPlayback() 开始定时推送数据
     * 6. startFrontendPlayback() 使用定时器按时间顺序推送数据到图表
     * 7. 对于每个时间点，直接将数据传递给 updateChartIncremental() 函数
     * 8. 回放结束或用户点击"停止回放"按钮时，调用 stopPlayback() 停止回放
     */
    
    // 回放控制全局变量
    let completeDataCache = null;
    let playbackTimer = null;
    let playbackIndex = 0;
    let chartManagerInstance = null;
    let processInitialData = null;
    let needsInitialization = true; // 标记是否需要初始化数据
    
    function startPlayback() {
      try {
        const date = document.getElementById('playback-date').value;
        const time = document.getElementById('playback-time').value;
        const speed = parseInt(document.getElementById('playback-speed').value, 10);
        
        if (!date) {
          alert('请选择日期');
          // 恢复按钮状态
          document.getElementById('start-playback-btn').disabled = false;
          document.getElementById('stop-playback-btn').disabled = true;
          return;
        }
        
        // 获取图表管理器实例
        if (!chartManagerInstance) {
          if (window.chartManagerInstance) {
            chartManagerInstance = window.chartManagerInstance;
          } else {
            console.error("找不到 chartManagerInstance");
            // 恢复按钮状态
            document.getElementById('start-playback-btn').disabled = false;
            document.getElementById('stop-playback-btn').disabled = true;
            return;
          }
        }
        
        // 检查是否已有缓存数据
        if (completeDataCache) {
          // 只有在需要初始化时才调用初始化函数
          if (needsInitialization) {
            initializePlaybackData();
            needsInitialization = false; // 初始化完成后重置标志
          }
          
          // 无论是否初始化，都开始前端回放
          startFrontendPlayback();
        } else {
          alert('没有可用的回放数据，请刷新页面或选择其他日期');
          // 恢复按钮状态
          document.getElementById('start-playback-btn').disabled = false;
          document.getElementById('stop-playback-btn').disabled = true;
        }
      } catch (error) {
        console.error('启动回放失败:', error);
        // 确保在出错时恢复按钮状态
        document.getElementById('start-playback-btn').disabled = false;
        document.getElementById('stop-playback-btn').disabled = true;
      }
    }
    
    function stopPlayback() {
      if (playbackTimer) {
        clearInterval(playbackTimer);
        playbackTimer = null;
      }
      
      // 更新按钮状态
      document.getElementById('start-playback-btn').disabled = false;
      document.getElementById('stop-playback-btn').disabled = true;
    }
    
    // 处理完整日线数据的函数
    function handleCompleteDailyData(data) {
      completeDataCache = data;
      playbackIndex = 0;
      console.log("handleCompleteDailyData", data);
      
      // 设置需要初始化的标志
      needsInitialization = true;
      
      // 初始化回放数据
      initializePlaybackData();
      
      // 更新界面信息
      if (data && data.date) {
        document.getElementById('current-time').textContent = `(${data.date})`;
      }
      
      if (data && data.stocks_data) {
        document.getElementById('stock-count').textContent = `${Object.keys(data.stocks_data || {}).length}`;
      }
    }
    
    // 暴露为全局函数，让main-npm.js能够调用
    window.handleCompleteDailyData = handleCompleteDailyData;
    
    // 初始化回放数据，预处理所有时间点的数据
    function initializePlaybackData() {
      if (!completeDataCache) {
        console.error("没有可用的回放数据");
        return;
      }
      
      // 确保 chartManagerInstance 已初始化
      if (!chartManagerInstance) {
        if (window.chartManagerInstance) {
          chartManagerInstance = window.chartManagerInstance;
        } else {
          console.error("在 initializePlaybackData 中找不到 chartManagerInstance");
          return;
        }
      }
      
      // 获取开始时间
      const dateInput = document.getElementById('playback-date').value;
      const timeInput = document.getElementById('playback-time').value;
      const selectDateTime = `${dateInput} ${timeInput}:00`;
      
      // 1. 收集所有唯一时间点并排序
      const allDateTimes = new Set();
      Object.values(completeDataCache.stocks_data || {}).forEach(stockData => {
        stockData.forEach(point => allDateTimes.add(point.time));
      });
      const sortedDateTimes = Array.from(allDateTimes).sort();
      
      // 2. 找到开始时间在数据中的位置
      let initialIndex = sortedDateTimes.findIndex(t => t >= selectDateTime);
      if (initialIndex === -1) initialIndex = 0;
      
      // 3. 预处理每个时间点的所有股票数据
      const timePointData = new Map(); // 时间点 -> 该时间点所有股票的数据
      
      sortedDateTimes.forEach(timePoint => {
        const stocksAtTimePoint = {};
        
        Object.keys(completeDataCache.stocks_data || {}).forEach(symbol => {
          const stockData = completeDataCache.stocks_data[symbol] || [];
          const dataAtTimePoint = stockData.filter(d => d.time === timePoint);
          
          if (dataAtTimePoint.length > 0) {
            stocksAtTimePoint[symbol] = dataAtTimePoint;
          }
        });
        
        timePointData.set(timePoint, stocksAtTimePoint);
      });
      
      // 4. 保存预处理数据和状态
      chartManagerInstance.playbackData = {
        timePoints: sortedDateTimes,
        initialIndex: initialIndex,
        currentIndex: initialIndex,
        timePointData: timePointData
      };
      
      // 5. 初始化显示开始时间之前的数据
      const symbols = Object.keys(completeDataCache.stocks_data || {});
      symbols.forEach(symbol => {
        if (!completeDataCache.stocks_data[symbol] || completeDataCache.stocks_data[symbol].length === 0) {
          return;
        }
        
        // 确保图表容器存在
        if (window.ensureChartExists) {
          window.ensureChartExists(symbol);
        }
        
        // 筛选开始时间之前的数据
        const initialData = completeDataCache.stocks_data[symbol].filter(d => d.time < selectDateTime);
        
        // 初始化空数组
        chartManagerInstance.oneMinData.set(symbol, []);
        
        // 更新图表显示初始数据
        if (initialData.length > 0) {
          // 初始批量数据加载：使用5分钟分组处理
          if (window.processInitialData) {
            const processedData = window.processInitialData(initialData, true);
            chartManagerInstance.updateChartIncremental(symbol, processedData);
          }
        }
      });
      
      // 设置回放状态
      if (typeof chartManagerInstance.setPlaybackSettings === 'function') {
        const speed = parseInt(document.getElementById('playback-speed').value);
        chartManagerInstance.setPlaybackSettings({
          startTime: selectDateTime,
          speed: speed,
          isPlaying: false
        });
      }
    }

    /**
     * 简化的前端回放函数 - 负责按时间顺序推送预处理好的数据到图表
     * 优化设计：使用预处理的时间点数据，避免回放过程中的重复计算
     */
    function startFrontendPlayback() {
      // 验证必要的数据和实例是否可用
      if (!completeDataCache || !chartManagerInstance || !chartManagerInstance.playbackData) {
        return; // 如果缺少必要数据，直接退出
      }
      
      // 获取用户设置的回放速度（毫秒）
      const speed = parseInt(document.getElementById('playback-speed').value);
      
      // 清除可能存在的之前的回放定时器，避免多个定时器同时运行
      if (playbackTimer) {
        clearInterval(playbackTimer);
      }
      
      // 从预处理数据中解构出时间点数组和每个时间点对应的股票数据
      const { timePoints, timePointData } = chartManagerInstance.playbackData;
      // 获取当前回放位置的索引（从初始化时设置的起始点开始）
      let { currentIndex } = chartManagerInstance.playbackData;
      
      // 设置定时器，按指定速度逐步推送数据
      playbackTimer = setInterval(() => {
        // 检查是否已到达所有时间点的末尾
        if (currentIndex >= timePoints.length) {
          // 回放结束，清理资源
          clearInterval(playbackTimer);
          playbackTimer = null;
          
          // 更新UI按钮状态，允许用户重新开始回放
          document.getElementById('start-playback-btn').disabled = false;
          document.getElementById('stop-playback-btn').disabled = true;
          return;
        }
        
        // 获取当前时间点及其对应的所有股票数据
        const currentTime = timePoints[currentIndex];
        const stocksData = timePointData.get(currentTime) || {};
        // console.log("currentIndex", currentIndex, currentTime, stocksData);
        
        // 遍历当前时间点的所有股票数据，分别更新每只股票的图表
        Object.entries(stocksData).forEach(([symbol, dataPoints]) => {
          // 直接使用当前时间点的数据更新图表，无需合并历史数据
          // updateChartIncremental 内部会自动进行数据比较和处理
          if (window.processInitialData) {
            // 使用全局处理函数进行数据预处理（如分组、填充等）
            console.log(`processInitialData ${symbol}`, dataPoints);
            const processedData = window.processInitialData(dataPoints, false);
            // 使用增量更新方式更新图表，提高性能
            chartManagerInstance.updateChartIncremental(symbol, processedData);
          }
        });
        
        // 更新时间点索引，准备下一个时间点的数据
        currentIndex++;
        // 同步更新到全局状态，确保暂停后可以从正确位置继续
        chartManagerInstance.playbackData.currentIndex = currentIndex;
        
      }, speed); // 根据用户设置的速度控制更新频率
    }
    
    // 开始回放按钮的处理函数
    function handleStartPlayback() {
      // 立即禁用开始按钮，启用停止按钮
      document.getElementById('start-playback-btn').disabled = true;
      document.getElementById('stop-playback-btn').disabled = false;
      
      // 调用原始的startPlayback函数
      startPlayback();
    }
    
    // 停止回放按钮的处理函数
    function handleStopPlayback() {
      // 调用原始的stopPlayback函数
      stopPlayback();
      
      // 确保按钮状态正确
      document.getElementById('start-playback-btn').disabled = false;
      document.getElementById('stop-playback-btn').disabled = true;
    }
    
    // 监听日期和时间选择变化，需要重新初始化
    document.getElementById('playback-date').addEventListener('change', function() {
      needsInitialization = true;
    });
    
    document.getElementById('playback-time').addEventListener('change', function() {
      needsInitialization = true;
    });
    
    // 监听页面加载事件
    document.addEventListener('DOMContentLoaded', function() {
      // 重置按钮状态
      document.getElementById('start-playback-btn').disabled = false;
      document.getElementById('stop-playback-btn').disabled = true;
      
      // 重置回放状态
      if (playbackTimer) {
        clearInterval(playbackTimer);
        playbackTimer = null;
      }
      playbackIndex = 0;
      needsInitialization = true; // 页面加载时需要初始化
      
      // 获取全局函数引用
      if (window.processInitialData) {
        processInitialData = window.processInitialData;
        console.log("成功获取 processInitialData 函数");
      }
      
      if (window.chartManagerInstance) {
        chartManagerInstance = window.chartManagerInstance;
        console.log("成功获取 chartManagerInstance");
      }
      
      // 自动请求当前选择的日期数据
      const dateInput = document.getElementById('playback-date');
      const timeInput = document.getElementById('playback-time');
      
      if (dateInput && timeInput && window.socket) {
        // 等待socket连接完成
        setTimeout(() => {
          if (window.socket && window.socket.connected) {
            const date = dateInput.value;
            window.socket.emit('complete_daily_data', { date }); // 使用complete_daily_data事件请求数据
          }
        }, 1000);
      }
    });
  </script>
  
  <!-- 主副系统模式标记：请在后端渲染时设置为 true（副系统/回放）或 false（主系统/实时） -->
  <script>window.isPlaybackMode = true; // 副系统回放模式，主系统请渲染为 false</script>
  <!-- NPM ES模块主入口 -->
  <script type="module" src="/static/js/main-npm.js"></script>
</body>
</html>
