import feedparser
import logging
import warnings
import threading
from futu import OpenQuoteContext as FutuQuoteContext

# 禁用 feedparser 的 count 参数警告
warnings.filterwarnings('ignore', message=r'.*count.*is passed as positional argument.*', 
                       module='feedparser.html')

from src.utils.futu_utils import set_stock_reminder
from src.utils.constants import *
from src.utils.date_time_utils import get_difference_time
from src.utils.sentiment_analysis_utils import get_sentiment
from src.utils.translator_utils import translator
from src.utils.popup import show_signal_message
from src.utils.db_utils import store_news_to_db
from src.utils.key_generator import key_generator

# 指定 RSS 源的 URL
rss_url = 'https://www.globenewswire.com/RssFeed/orgclass/1/feedTitle/GlobeNewswire%20-%20News%20about%20Public%20Companies'
allowed_exchanges = {'nasdaq', 'amex', 'nyse'} # 集合查找更快

def get_globenewswire_news(known_tickers: set[str], manager_res_dict_test: dict, quote_ctx: FutuQuoteContext, news_stock_dict: dict, news_stock_dict_lock: threading.Lock) -> None:
    """
    通过 globenewswire RSS 获取股票新闻
    :param known_tickers: 已知的股票代号列表
    :param manager_res_dict_test: 多线程资源共享字典
    :param quote_ctx: 富途客户端实例
    :return: 股票新闻
    """
    # 解析 RSS 源
    try:
        feed = feedparser.parse(rss_url)
    except Exception as e:
        logging.error(f"globenewswire Failed to fetch or parse RSS feed: {e}")
        return

    # 遍历并输出每个条目的标题和链接
    for entry in feed.entries:
        tags = getattr(entry, 'tags', [])
        if not tags:
            continue
        first_ticker_symbol = next(
            (
                tag['term'].split(':')[1]
                for tag in entry.tags
                if tag.get('scheme') == 'https://www.globenewswire.com/rss/stock'
                and tag['term'].split(':')[0].lower() in allowed_exchanges
            ),
            None
        )

        # 检查是否在已知股票代号列表中
        if not first_ticker_symbol or first_ticker_symbol not in known_tickers:
            continue
        # 时间差，新闻发布时间
        difference_time, formatted_given_date, formatted_given_date_d, formatted_given_date_y, current_timestamp = get_difference_time(entry.published)
        if '现在' not in difference_time and '分钟' not in difference_time:
            continue
        # 新闻情绪
        sentiment = get_sentiment(entry.description)
        # 执行翻译
        translation_title = translator.translate(entry.title.replace("\n", "")).replace(" ", "").replace("，", ",")

        # 获取股票的所有键值
        keys = key_generator.get_keys_for_stock(first_ticker_symbol)
        
        # 检查 formatted_given_date 是否已存在于 manager_res_dict 中
        existing_entries = manager_res_dict_test.get(keys[NEWS], [])
        # 通过遍历判断 formatted_given_date 是否已存在
        if not any(f'g {formatted_given_date}' in entry for entry in existing_entries):
            # 构造新条目
            new_entry = f'{sentiment} {difference_time} g {formatted_given_date}\n{translation_title}'
            # 设置股票提醒
            set_stock_reminder(first_ticker_symbol, quote_ctx, 'globenewswire RSS')
            # 如果不存在，则添加新条目
            manager_res_dict_test[keys[NEWS]] = existing_entries + [new_entry]
            manager_res_dict_test[keys[SET_FLG]] = False
            show_signal_message(first_ticker_symbol, new_entry)
            with news_stock_dict_lock:
                news_stock_dict[first_ticker_symbol] = current_timestamp
            # 将新闻存储到数据库中
            store_news_to_db(first_ticker_symbol, 'g', formatted_given_date_d, formatted_given_date_y, new_entry)
        logging.info(f"globenewswire RSS {first_ticker_symbol} Title: {translation_title}\nDate: {formatted_given_date} {difference_time}\nSentiment: {sentiment}\ndescription{entry.description}\nnews_stock_dict {news_stock_dict}\n\n")
    
# known_tickers = {'AEON', 'INLF', 'XYZ'}
# manager_res_dict_test = {}
# manager_res_dict_test[f'AEON_{NEWS}'] = []
# manager_res_dict_test[f'INLF_{NEWS}'] = []
# get_globenewswire_news(known_tickers, manager_res_dict_test, None)
