import feedparser
import logging
import warnings
import threading
from futu import OpenQuoteContext as FutuQuoteContext

# 禁用 feedparser 的 count 参数警告
warnings.filterwarnings('ignore', message=r'.*count.*is passed as positional argument.*', 
                       module='feedparser.html')

from src.utils.futu_utils import set_stock_reminder
from src.utils.constants import *
from src.utils.date_time_utils import get_difference_time
from src.utils.sentiment_analysis_utils import get_sentiment
from src.utils.translator_utils import translator
from src.utils.popup import show_signal_message
from src.utils.db_utils import store_news_to_db
from src.utils.key_generator import key_generator

# 指定 RSS 源的 URL
rss_url = 'https://www.stocktitan.net/rss'

def get_stocktitan_news(known_tickers: set[str], manager_res_dict_test: dict, quote_ctx: FutuQuoteContext, news_stock_dict: dict, news_stock_dict_lock: threading.Lock) -> None:
    """
    通过 stocktitan RSS 获取股票新闻
    :param known_tickers: 已知的股票代号列表
    :param manager_res_dict_test: 多线程资源共享字典
    :param quote_ctx: 富途客户端实例
    :return: 股票新闻
    """
    # 解析 RSS 源
    try:
        feed = feedparser.parse(rss_url)
    except Exception as e:
        logging.error(f"stocktitan Failed to fetch or parse RSS feed: {e}")
        return

    # 遍历并输出每个条目的标题和链接
    for entry in feed.entries:
        # 提取股票代号
        # 假设股票代号在链接或 GUID 中，如 `/FAST/`
        link = entry.get('link', '')
        stock_code = link.split('/')[4] 
        # 检查是否在已知股票代号列表中
        if not stock_code or stock_code not in known_tickers:
            continue

        # 提取发布时间
        pub_date = entry.get('published', '')
        # 时间差，新闻发布时间
        difference_time, formatted_given_date, formatted_given_date_d, formatted_given_date_y, current_timestamp = get_difference_time(pub_date)
        if '现在' not in difference_time and '分钟' not in difference_time:
            continue
        # 提取标题
        title = entry.get('title', '无标题')
        # 新闻情绪
        sentiment = get_sentiment(title)
        # 执行翻译
        translation_title = translator.translate(title.replace("\n", "")).replace(" ", "").replace("，", ",")
       
        # 获取股票的所有键值
        keys = key_generator.get_keys_for_stock(stock_code)

        # 检查 formatted_given_date 是否已存在于 manager_res_dict 中
        existing_entries = manager_res_dict_test.get(keys[NEWS], [])
        # 通过遍历判断 formatted_given_date 是否已存在
        if not any(f'st {formatted_given_date}' in entry for entry in existing_entries):
            # 构造新条目
            new_entry = f'{sentiment} {difference_time} st {formatted_given_date}\n{translation_title}'
            # 设置股票提醒
            set_stock_reminder(stock_code, quote_ctx, 'stocktitan RSS')
            show_signal_message(stock_code, new_entry)
            manager_res_dict_test[keys[NEWS]] = existing_entries + [new_entry]
            manager_res_dict_test[keys[SET_FLG]] = False
            with news_stock_dict_lock:
                news_stock_dict[stock_code] = current_timestamp
            # 将新闻存储到数据库中
            store_news_to_db(stock_code, 'st', formatted_given_date_d, formatted_given_date_y, new_entry)
        logging.info(f"stocktitan RSS {stock_code} Title: {translation_title}\nDate: {formatted_given_date} {difference_time}\nSentiment: {sentiment}\nnews_stock_dict {news_stock_dict}\n\n")
    
# known_tickers = {'KWE', 'GNTY', 'XYZ'}
# manager_res_dict_test = {}
# manager_res_dict_test[f'KWE_{NEWS}'] = ['stocktitan RSS KWE Title: KWESST宣布增加管理团队和董事会成员|KWE股票新闻\nDate: 01/06 07:15 9分钟前\nSentiment: 中性']
# manager_res_dict_test[f'GNTY_{NEWS}'] = []
# get_stocktitan_news(known_tickers, manager_res_dict_test, None)
