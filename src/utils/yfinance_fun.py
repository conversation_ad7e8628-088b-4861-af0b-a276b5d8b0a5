"""Yahoo Finance 数据获取模块"""

import yfinance as yf
import logging
from src.utils.constants import (
    STOCK_SHARESOUTSTANDING,
    SET_FLG
)
from src.utils.key_generator import key_generator
from src.data.data_manager_adapter import manager_res_dict_test_adapter

def get_stock_sharesOutstanding(stock_code):
    """ 获取股票流通股 """
    try:
        # 获取股票的所有键值
        keys = key_generator.get_keys_for_stock(stock_code)

        # 创建股票对象
        stock = yf.Ticker(stock_code)
        # 获取股票的基本信息
        info = stock.info
        # 使用新的适配器存储数据
        manager_res_dict_test_adapter[keys[STOCK_SHARESOUTSTANDING]] = info.get('sharesOutstanding')
        manager_res_dict_test_adapter[keys[SET_FLG]] = False
    except Exception as e:
        manager_res_dict_test_adapter[keys[STOCK_SHARESOUTSTANDING]] = float('inf')
        logging.error(f'{stock_code} {e}')

# def get_avg_volume(stock_code, manager_res_dict):
#     """ 获取5日平均成交量 """
#     # 获取今天日期
#     end_date = datetime.now(tz=ny_timezone)
#     # 获取10天前的日期
#     start_date = end_date - timedelta(days=10)
#     # 格式化日期为字符串（'YYYY-MM-DD'）
#     data = yf.Ticker(stock_code).history(start=start_date.strftime('%Y-%m-%d'), end=end_date.strftime('%Y-%m-%d'), interval="1d")

#     # 检查是否获取到了成交量数据
#     if 'Volume' in data.columns:
#         # 计算过去5天的平均成交量
#         avg_volume_5d = data['Volume'][-5:].mean()
#         manager_res_dict[f'{stock_code}_{VOLUME_AVG}'] = format_volume(avg_volume_5d)
        # print(f'=============={stock_code} {data['Volume']}---------------------------')

# manager_res_dict = {}
# get_stock_sharesOutstanding('JTAI', manager_res_dict)