import os
import logging
from datetime import datetime
from src.utils.key_generator import KeyGenerator
from src.utils.constants import NOW_PRICE, PRICE_CHANGE_THRESHOLD, DATA_CACHE_DIR, SAVE_COOLDOWN
import pandas as pd
from src.utils.db_utils import insert_ticker_data_to_db, insert_orderbook_data_to_db

def handle_price_change_and_cache(
    cache_key: str,
    cache_entry: dict,
    now: datetime,
    to_day: str,
    manager_res_dict_test: dict,
    key_generator: KeyGenerator,
    data_cache: dict
) -> None:
    """
    封装价格变化判断、数据保存、缓存删除等逻辑。

    Args:
        cache_key: 缓存键
        cache_entry: 缓存数据
        now: 当前时间
        to_day: 当前日期
        manager_res_dict_test: 市场数据字典
        key_generator: 键生成器
        data_cache: 数据缓存
        
    Returns:
        None
    """
    try:
        # 解析缓存键
        stock_code, timestamp_str = cache_key
        # 检查是否已经超过过期时间
        cache_time = datetime.strptime(cache_entry['timestamp'], '%Y-%m-%d_%H_%M_%S')
        # 获取当前价格
        keys = key_generator.get_keys_for_stock(stock_code)
        current_price = manager_res_dict_test.get(keys[NOW_PRICE], 0)
        # 计算价格变化百分比
        base_price = cache_entry['base_price']

        price_change = (current_price - base_price) / base_price
        # 如果基准价格大于0且价格变化超过阈值，则保存数据
        if (base_price > 0 and price_change >= PRICE_CHANGE_THRESHOLD):
            logging.info(f"股票 {stock_code} 当前价格: {current_price:.4f}, 基准价格: {base_price:.4f}, 变化: {price_change * 100:.2%}")
            
            try:
                # 保存ticker数据到MySQL
                insert_ticker_data_to_db(cache_entry['ticker_data'])
                # 保存orderbook数据到MySQL
                insert_orderbook_data_to_db(cache_entry['orderbook_data'])
                logging.info(f"股票 {stock_code}，数据已插入MySQL")
            except Exception as e:
                logging.error(f"股票 {stock_code} 插入数据库时出错 {stock_code}: {str(e)}", exc_info=True)

        # 无论是否保存，都从缓存中删除过期数据
        if (base_price > 0 and price_change >= PRICE_CHANGE_THRESHOLD)\
            or (now - cache_time).total_seconds() > SAVE_COOLDOWN * 60:
            del data_cache[cache_key]
            
    except Exception as e:
        logging.error(f"股票 {stock_code} 处理缓存键 {cache_key} 时出错: {str(e)}", exc_info=True) 

def clean_expired_data(data_dict: dict, cutoff_time: datetime, data_type_name: str = "ticker") -> None:
    """
    清理数据字典中过期的数据，只保留date_time >= cutoff_time的数据。
    支持ticker/orderbook等任意结构，自动日志输出清理前后行数。
    Args:
        data_dict: 股票代码到DataFrame的字典
        cutoff_time: 截止时间（datetime，无时区）
        data_type_name: 日志用的数据类型名（如"ticker"/"orderbook"）
        
    Returns:
        None
    """
    for stock_code in list(data_dict.keys()):
        df = data_dict[stock_code]
        if df.empty:
            continue
        try:
            # 统一转换为datetime类型
            df['date_time'] = pd.to_datetime(df['date_time'], errors='coerce')
            # 裁剪数据
            before_rows = len(df)
            df = df[df['date_time'] >= cutoff_time].sort_values('date_time')
            after_rows = len(df)
            data_dict[stock_code] = df
            if before_rows > after_rows:
                logging.info(f"股票 {stock_code} 的{data_type_name}数据从 {before_rows} 条减少到 {after_rows} 条")
        except Exception as e:
            logging.error(f"处理{data_type_name}数据时出错 {stock_code}: {str(e)}", exc_info=True)
            continue 